{
  "name": "JimThread Development Environment",
  "dockerComposeFile": "docker-compose.yml",
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  "shutdownAction": "stopCompose",
  
  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "settings": {
        "terminal.integrated.defaultProfile.linux": "bash",
        "dotnet.defaultSolution": "src/backend/JimThread.sln",
        "omnisharp.enableRoslynAnalyzers": true,
        "omnisharp.enableEditorConfigSupport": true,
        "omnisharp.enableImportCompletion": true,
        "eslint.workingDirectories": ["src/frontend"],
        "typescript.preferences.importModuleSpecifier": "relative",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": true,
          "source.organizeImports": true
        },
        "files.exclude": {
          "**/node_modules": true,
          "**/bin": true,
          "**/obj": true
        }
      },
      
      "extensions": [
        // .NET Development
        "ms-dotnettools.csharp",
        "ms-dotnettools.vscode-dotnet-runtime",
        "ms-dotnettools.blazorwasm-companion",
        
        // React/TypeScript Development
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        
        // Docker & Kubernetes
        "ms-azuretools.vscode-docker",
        "ms-kubernetes-tools.vscode-kubernetes-tools",
        
        // Git & Version Control
        "eamodio.gitlens",
        "github.vscode-pull-request-github",
        
        // General Development
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.hexeditor",
        "ms-vscode.vscode-markdown",
        
        // Testing
        "ms-dotnettools.vscode-dotnet-test-explorer",
        "hbenl.vscode-test-explorer",
        
        // Code Quality
        "sonarsource.sonarlint-vscode",
        "streetsidesoftware.code-spell-checker",
        
        // Database
        "ms-mssql.mssql",
        "neo4j.neo4j",
        
        // API Development
        "humao.rest-client",
        "42crunch.vscode-openapi"
      ]
    }
  },
  
  // Use 'forwardPorts' to make a list of ports inside the container available locally
  "forwardPorts": [
    5000,  // API Gateway
    5001,  // API Gateway HTTPS
    3000,  // React Dev Server
    7474,  // Neo4j Browser
    7687,  // Neo4j Bolt
    9092,  // Kafka
    6379,  // Redis
    9090,  // Prometheus
    3001   // Grafana
  ],
  
  // Configure port attributes
  "portsAttributes": {
    "5000": {
      "label": "API Gateway (HTTP)",
      "onAutoForward": "notify"
    },
    "5001": {
      "label": "API Gateway (HTTPS)",
      "onAutoForward": "notify"
    },
    "3000": {
      "label": "React Dev Server",
      "onAutoForward": "openBrowser"
    },
    "7474": {
      "label": "Neo4j Browser",
      "onAutoForward": "notify"
    }
  },
  
  // Use 'postCreateCommand' to run commands after the container is created
  "postCreateCommand": "bash .devcontainer/post-create.sh",
  
  // Use 'postStartCommand' to run commands after the container starts
  "postStartCommand": "bash .devcontainer/post-start.sh",
  
  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root
  "remoteUser": "vscode",
  
  // Set container environment variables
  "containerEnv": {
    "DOTNET_CLI_TELEMETRY_OPTOUT": "1",
    "DOTNET_SKIP_FIRST_TIME_EXPERIENCE": "1",
    "ASPNETCORE_ENVIRONMENT": "Development",
    "NODE_ENV": "development"
  },
  
  // Mount the Docker socket for Docker-in-Docker scenarios
  "mounts": [
    "source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
  ],
  
  // Configure container features
  "features": {
    "ghcr.io/devcontainers/features/common-utils:2": {
      "installZsh": true,
      "configureZshAsDefaultShell": true,
      "installOhMyZsh": true,
      "upgradePackages": true
    },
    "ghcr.io/devcontainers/features/git:1": {
      "ppa": true,
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {
      "installDirectlyFromGitHubRelease": true,
      "version": "latest"
    }
  }
}
