#!/bin/bash

# JimThread Development Environment Post-Start Script
# This script runs every time the dev container starts

set -e

echo "🔄 Starting JimThread development services..."

# Ensure we're in the workspace directory
cd /workspace

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not available in the container"
    exit 1
fi

# Wait for Docker daemon to be ready
echo "⏳ Waiting for Docker daemon..."
while ! docker info &> /dev/null; do
    sleep 1
done

echo "✅ Docker daemon is ready"

# Start development services if docker-compose.yml exists
if [ -f ".devcontainer/docker-compose.yml" ]; then
    echo "🐳 Starting development services..."
    
    # Start the services in the background
    docker-compose -f .devcontainer/docker-compose.yml up -d neo4j kafka redis prometheus grafana kafka-ui
    
    echo "⏳ Waiting for services to be ready..."
    
    # Wait for Neo4j to be ready
    echo "Waiting for Neo4j..."
    until docker-compose -f .devcontainer/docker-compose.yml exec -T neo4j cypher-shell -u neo4j -p devpassword "RETURN 1" &> /dev/null; do
        sleep 2
    done
    echo "✅ Neo4j is ready"
    
    # Wait for Kafka to be ready
    echo "Waiting for Kafka..."
    until docker-compose -f .devcontainer/docker-compose.yml exec -T kafka kafka-topics --bootstrap-server localhost:9092 --list &> /dev/null; do
        sleep 2
    done
    echo "✅ Kafka is ready"
    
    # Wait for Redis to be ready
    echo "Waiting for Redis..."
    until docker-compose -f .devcontainer/docker-compose.yml exec -T redis redis-cli ping | grep PONG &> /dev/null; do
        sleep 2
    done
    echo "✅ Redis is ready"
    
    echo "🎉 All development services are ready!"
else
    echo "⚠️ Docker Compose file not found, skipping service startup"
fi

# Set up Kafka topics for JimThread
echo "📡 Setting up Kafka topics..."
if command -v docker-compose &> /dev/null; then
    # Create essential topics for JimThread
    docker-compose -f .devcontainer/docker-compose.yml exec -T kafka kafka-topics --create --if-not-exists --bootstrap-server localhost:9092 --topic sensor-data --partitions 3 --replication-factor 1
    docker-compose -f .devcontainer/docker-compose.yml exec -T kafka kafka-topics --create --if-not-exists --bootstrap-server localhost:9092 --topic asset-events --partitions 3 --replication-factor 1
    docker-compose -f .devcontainer/docker-compose.yml exec -T kafka kafka-topics --create --if-not-exists --bootstrap-server localhost:9092 --topic automation-actions --partitions 3 --replication-factor 1
    docker-compose -f .devcontainer/docker-compose.yml exec -T kafka kafka-topics --create --if-not-exists --bootstrap-server localhost:9092 --topic anomaly-alerts --partitions 3 --replication-factor 1
    echo "✅ Kafka topics created"
fi

# Initialize Neo4j with basic schema
echo "🗄️ Initializing Neo4j schema..."
if command -v docker-compose &> /dev/null; then
    # Create basic constraints and indexes
    docker-compose -f .devcontainer/docker-compose.yml exec -T neo4j cypher-shell -u neo4j -p devpassword "
    CREATE CONSTRAINT asset_id_unique IF NOT EXISTS FOR (a:Asset) REQUIRE a.id IS UNIQUE;
    CREATE CONSTRAINT sensor_id_unique IF NOT EXISTS FOR (s:Sensor) REQUIRE s.id IS UNIQUE;
    CREATE CONSTRAINT user_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.id IS UNIQUE;
    CREATE INDEX asset_name_index IF NOT EXISTS FOR (a:Asset) ON (a.name);
    CREATE INDEX sensor_tag_index IF NOT EXISTS FOR (s:Sensor) ON (s.tag);
    CREATE INDEX timestamp_index IF NOT EXISTS FOR (e:Event) ON (e.timestamp);
    "
    echo "✅ Neo4j schema initialized"
fi

# Display service status
echo ""
echo "🌟 JimThread Development Environment Status:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🔗 Service URLs:"
echo "   • API Gateway: https://localhost:5001"
echo "   • React App: http://localhost:3000"
echo "   • Neo4j Browser: http://localhost:7474 (neo4j/devpassword)"
echo "   • Kafka UI: http://localhost:8080"
echo "   • Prometheus: http://localhost:9090"
echo "   • Grafana: http://localhost:3001 (admin/devpassword)"
echo ""
echo "🚀 Quick Commands:"
echo "   • Start API: cd src/backend && dotnet run --project JimThread.Api"
echo "   • Start Frontend: cd src/frontend && npm start"
echo "   • View Logs: docker-compose -f .devcontainer/docker-compose.yml logs -f"
echo "   • Stop Services: docker-compose -f .devcontainer/docker-compose.yml down"
echo ""
echo "✨ Happy coding with JimThread!"
