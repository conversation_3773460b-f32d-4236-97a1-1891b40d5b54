#!/bin/bash

# JimThread Development Environment Post-Create Script
# This script runs after the dev container is created

set -e

echo "🚀 Setting up JimThread development environment..."

# Ensure we're in the workspace directory
cd /workspace

# Create directory structure if it doesn't exist
echo "📁 Creating project structure..."
mkdir -p src/backend
mkdir -p src/frontend
mkdir -p src/shared
mkdir -p infrastructure/docker
mkdir -p infrastructure/kubernetes
mkdir -p infrastructure/terraform
mkdir -p infrastructure/helm
mkdir -p tests
mkdir -p docs
mkdir -p tools

# Set up .NET backend structure
echo "🔧 Setting up .NET backend structure..."
cd src/backend

# Create solution file if it doesn't exist
if [ ! -f "JimThread.sln" ]; then
    dotnet new sln -n JimThread
fi

# Create project directories
mkdir -p JimThread.Api
mkdir -p JimThread.Core
mkdir -p JimThread.Connectors
mkdir -p JimThread.Analytics
mkdir -p JimThread.Automation
mkdir -p JimThread.Discovery
mkdir -p JimThread.Infrastructure

# Return to workspace root
cd /workspace

# Set up React frontend
echo "⚛️ Setting up React frontend..."
cd src/frontend

# Create package.json if it doesn't exist
if [ ! -f "package.json" ]; then
    cat > package.json << 'EOF'
{
  "name": "jimthread-frontend",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@mui/icons-material": "^5.14.19",
    "@mui/material": "^5.14.20",
    "@mui/x-charts": "^6.18.1",
    "@mui/x-data-grid": "^6.18.1",
    "@mui/x-date-pickers": "^6.18.1",
    "@tanstack/react-query": "^5.8.4",
    "@types/node": "^20.9.0",
    "@types/react": "^18.2.37",
    "@types/react-dom": "^18.2.15",
    "axios": "^1.6.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.18.0",
    "react-scripts": "5.0.1",
    "recharts": "^2.8.0",
    "typescript": "^5.2.2",
    "web-vitals": "^3.5.0"
  },
  "devDependencies": {
    "@testing-library/jest-dom": "^6.1.4",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^14.5.1",
    "@types/jest": "^29.5.8",
    "@typescript-eslint/eslint-plugin": "^6.11.0",
    "@typescript-eslint/parser": "^6.11.0",
    "eslint": "^8.54.0",
    "eslint-plugin-react": "^7.33.2",
    "eslint-plugin-react-hooks": "^4.6.0",
    "prettier": "^3.1.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix",
    "format": "prettier --write src/**/*.{ts,tsx,json,css,md}"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}
EOF
fi

# Return to workspace root
cd /workspace

# Set up Git hooks
echo "🔗 Setting up Git hooks..."
if [ -d ".git" ]; then
    # Create pre-commit hook for code formatting
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# JimThread pre-commit hook

echo "Running pre-commit checks..."

# Check if there are any staged files
if git diff --cached --name-only | grep -E '\.(cs|ts|tsx|js|jsx)$' > /dev/null; then
    echo "Formatting code..."
    
    # Format .NET code
    if git diff --cached --name-only | grep -E '\.cs$' > /dev/null; then
        dotnet format --include $(git diff --cached --name-only | grep -E '\.cs$' | tr '\n' ' ')
    fi
    
    # Format TypeScript/React code
    if git diff --cached --name-only | grep -E '\.(ts|tsx|js|jsx)$' > /dev/null; then
        cd src/frontend
        npm run lint:fix
        npm run format
        cd /workspace
    fi
    
    # Re-stage formatted files
    git add $(git diff --cached --name-only)
fi

echo "Pre-commit checks completed."
EOF
    chmod +x .git/hooks/pre-commit
fi

# Set up environment files
echo "🔐 Setting up environment configuration..."
cat > .env.development << 'EOF'
# JimThread Development Environment Configuration

# API Configuration
ASPNETCORE_ENVIRONMENT=Development
API_BASE_URL=https://localhost:5001
CORS_ORIGINS=http://localhost:3000,https://localhost:3000

# Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=devpassword

# Message Queue Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Cache Configuration
REDIS_CONNECTION_STRING=localhost:6379

# Monitoring Configuration
PROMETHEUS_URL=http://localhost:9090
GRAFANA_URL=http://localhost:3001

# Security Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-for-development-only
JWT_ISSUER=JimThread
JWT_AUDIENCE=JimThread.Api

# Logging Configuration
SERILOG_MINIMUM_LEVEL=Debug
SERILOG_WRITE_TO_CONSOLE=true

# Feature Flags
ENABLE_SWAGGER=true
ENABLE_DETAILED_ERRORS=true
ENABLE_CORS=true
EOF

# Create VS Code workspace settings
echo "⚙️ Setting up VS Code workspace..."
mkdir -p .vscode
cat > .vscode/settings.json << 'EOF'
{
  "dotnet.defaultSolution": "src/backend/JimThread.sln",
  "omnisharp.enableRoslynAnalyzers": true,
  "omnisharp.enableEditorConfigSupport": true,
  "omnisharp.enableImportCompletion": true,
  "eslint.workingDirectories": ["src/frontend"],
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/bin": true,
    "**/obj": true,
    "**/.git": false
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/bin": true,
    "**/obj": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/bin/**": true,
    "**/obj/**": true
  }
}
EOF

# Create launch configuration for debugging
cat > .vscode/launch.json << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch API Gateway",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/src/backend/JimThread.Api/bin/Debug/net8.0/JimThread.Api.dll",
      "args": [],
      "cwd": "${workspaceFolder}/src/backend/JimThread.Api",
      "stopAtEntry": false,
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "sourceFileMap": {
        "/Views": "${workspaceFolder}/Views"
      }
    },
    {
      "name": "Attach to .NET Process",
      "type": "coreclr",
      "request": "attach"
    }
  ]
}
EOF

# Create tasks configuration
cat > .vscode/tasks.json << 'EOF'
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "build",
      "command": "dotnet",
      "type": "process",
      "args": [
        "build",
        "${workspaceFolder}/src/backend/JimThread.sln",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "publish",
      "command": "dotnet",
      "type": "process",
      "args": [
        "publish",
        "${workspaceFolder}/src/backend/JimThread.sln",
        "/property:GenerateFullPaths=true",
        "/consoleloggerparameters:NoSummary"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "watch",
      "command": "dotnet",
      "type": "process",
      "args": [
        "watch",
        "run",
        "--project",
        "${workspaceFolder}/src/backend/JimThread.Api"
      ],
      "problemMatcher": "$msCompile"
    },
    {
      "label": "start-frontend",
      "type": "shell",
      "command": "npm",
      "args": ["start"],
      "options": {
        "cwd": "${workspaceFolder}/src/frontend"
      },
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    }
  ]
}
EOF

echo "✅ JimThread development environment setup completed!"
echo ""
echo "🎯 Next steps:"
echo "1. Restart VS Code to apply all settings"
echo "2. Run 'dotnet restore' in src/backend/"
echo "3. Run 'npm install' in src/frontend/"
echo "4. Start the development services with 'docker-compose up -d'"
echo ""
echo "🔗 Development URLs:"
echo "- API Gateway: https://localhost:5001"
echo "- React App: http://localhost:3000"
echo "- Neo4j Browser: http://localhost:7474"
echo "- Kafka UI: http://localhost:8080"
echo "- Prometheus: http://localhost:9090"
echo "- Grafana: http://localhost:3001"
