# Prometheus Configuration for JimThread Development Environment

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # JimThread API Gateway
  - job_name: 'jimthread-api'
    static_configs:
      - targets: ['devcontainer:5000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # JimThread Analytics Service
  - job_name: 'jimthread-analytics'
    static_configs:
      - targets: ['devcontainer:5002']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # JimThread Automation Service
  - job_name: 'jimthread-automation'
    static_configs:
      - targets: ['devcontainer:5003']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # JimThread Discovery Service
  - job_name: 'jimthread-discovery'
    static_configs:
      - targets: ['devcontainer:5004']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Neo4j Database
  - job_name: 'neo4j'
    static_configs:
      - targets: ['neo4j:2004']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Kafka
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9308']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter (if available)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['devcontainer:9100']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
