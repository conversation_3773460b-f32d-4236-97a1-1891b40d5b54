# JimThread Platform - Local Development Environment
# This docker-compose file sets up the complete development environment

version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: ./src/backend/JimThread.Api
      dockerfile: Dockerfile
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=devpassword
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=devpassword
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_CONNECTION_STRING=redis:6379
    volumes:
      - ~/.aspnet/https:/https:ro
    depends_on:
      - neo4j
      - kafka
      - redis
    networks:
      - jimthread

  # Analytics Service
  analytics-service:
    build:
      context: ./src/backend/JimThread.Analytics
      dockerfile: Dockerfile
    ports:
      - "5002:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=devpassword
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_CONNECTION_STRING=redis:6379
    depends_on:
      - neo4j
      - kafka
      - redis
    networks:
      - jimthread

  # Automation Service
  automation-service:
    build:
      context: ./src/backend/JimThread.Automation
      dockerfile: Dockerfile
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=devpassword
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_CONNECTION_STRING=redis:6379
    depends_on:
      - neo4j
      - kafka
      - redis
    networks:
      - jimthread

  # Discovery Service
  discovery-service:
    build:
      context: ./src/backend/JimThread.Discovery
      dockerfile: Dockerfile
    ports:
      - "5004:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=devpassword
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_CONNECTION_STRING=redis:6379
    depends_on:
      - neo4j
      - kafka
      - redis
    networks:
      - jimthread

  # React Frontend
  frontend:
    build:
      context: ./src/frontend
      dockerfile: Dockerfile
      target: development
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_BASE_URL=https://localhost:5001
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./src/frontend:/app
      - /app/node_modules
    depends_on:
      - api-gateway
    networks:
      - jimthread

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5.15-community
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/devpassword
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - jimthread

  # Apache Kafka Message Broker
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - jimthread

  kafka:
    image: confluentinc/cp-kafka:7.5.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: 1073741824
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - jimthread

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - jimthread

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:v2.47.0
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - jimthread

  # Grafana Visualization
  grafana:
    image: grafana/grafana:10.2.0
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=devpassword
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - jimthread

  # Kafka UI for Development
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    ports:
      - "8080:8080"
    environment:
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS=kafka:29092
      - KAFKA_CLUSTERS_0_ZOOKEEPER=zookeeper:2181
    depends_on:
      - kafka
    networks:
      - jimthread

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
      - frontend
    networks:
      - jimthread

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  kafka_data:
  zookeeper_data:
  zookeeper_logs:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  jimthread:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
