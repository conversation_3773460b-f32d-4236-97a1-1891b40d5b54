# JimThread: Unified Operational Intelligence Platform

JimThread is the operating system for the intelligent factory. Our mission is to empower industrial operations to achieve maximum efficiency by providing a single, AI-native platform that transforms disconnected data into autonomous action.

## 🏗️ Architecture Overview

JimThread is built around a continuous, four-stage feedback loop:

1. **Connect & Model**: Build rich, dynamic digital twins by connecting to any data source
2. **Analyze & Understand**: Move beyond correlation to causation using AI tools
3. **Act & Automate**: Translate understanding into action with closed-loop process control
4. **Learn & Evolve**: Platform learns from every action, becoming progressively smarter

## 🚀 Quick Start

### Prerequisites

- Docker Desktop with Dev Containers support
- Visual Studio Code with Dev Containers extension
- Git

### Development Environment

1. Clone the repository:
```bash
git clone <repository-url>
cd OIL
```

2. Open in VS Code and reopen in Dev Container:
```bash
code .
# VS Code will prompt to reopen in container
```

3. The dev container will automatically set up:
   - .NET 8 SDK
   - Node.js 20 LTS
   - Docker CLI
   - Required development tools

## 📁 Project Structure

```
├── src/
│   ├── backend/              # C# .NET Backend Services
│   │   ├── JimThread.Api/           # Main API Gateway
│   │   ├── JimThread.Core/          # Shared Core Library
│   │   ├── JimThread.Connectors/    # Data Source Connectors
│   │   ├── JimThread.Analytics/     # Analysis & AI Services
│   │   ├── JimThread.Automation/    # Rules & Action Engine
│   │   └── JimThread.Discovery/     # Asset Discovery Service
│   ├── frontend/             # React Frontend Application
│   └── shared/               # Shared Types & Contracts
├── infrastructure/           # Infrastructure as Code
│   ├── docker/              # Docker configurations
│   ├── kubernetes/          # K8s manifests
│   └── terraform/           # Cloud infrastructure
├── tests/                   # Test projects
├── docs/                    # Documentation
└── tools/                   # Development tools & scripts
```

## 🛠️ Technology Stack

### Backend
- **.NET 8**: Latest LTS framework
- **ASP.NET Core**: Web API framework
- **Neo4j**: Graph database for asset modeling
- **Apache Kafka**: Message streaming platform
- **Redis**: Caching and session storage

### Frontend
- **React 18**: UI framework
- **Material-UI (MUI)**: Component library
- **React Query**: Data fetching and caching
- **Recharts**: Data visualization

### Infrastructure
- **Kubernetes**: Container orchestration
- **Docker**: Containerization
- **Helm**: Package management
- **Prometheus/Grafana**: Monitoring
- **GitHub Actions**: CI/CD

## 🔧 Development Guidelines

### Code Quality
- **C# Backend**: Follow SOLID principles, use dependency injection
- **React Frontend**: Functional components with hooks, MUI styling
- **Testing**: Minimum 80% code coverage
- **Code Reviews**: All changes require peer review

### Security
- **Data Encryption**: TLS 1.3 in transit, AES-256 at rest
- **RBAC**: Granular role-based access control
- **Compliance**: SOC 2, ISO 27001 ready

## 📊 MVP Features

The Minimum Viable Product includes:

### Stage 1: Connect & Model
- ✅ Connect to industrial historians
- ✅ Simple digital twin creation
- ✅ Data stream mapping

### Stage 2: Analyze & Understand
- ✅ High-performance time-series charts
- ✅ Unsupervised anomaly detection
- ✅ Natural language query interface

### Stage 3: Act & Automate
- ✅ Basic alerting system (email, in-app)
- ✅ Webhook triggers for anomalies
- ✅ Simple automation rules

## 🚀 Deployment

### Local Development
```bash
docker-compose up -d
```

### Production (Kubernetes)
```bash
helm install jimthread ./infrastructure/helm/jimthread
```

## 📈 Monitoring & Observability

- **Metrics**: Prometheus + Grafana
- **Logging**: Fluentd + Elasticsearch
- **Tracing**: Jaeger
- **Uptime**: 99.9% SLA target

## 🤝 Contributing

1. Create feature branch from `main`
2. Implement changes with tests
3. Submit pull request
4. Code review and approval
5. Automated deployment

## 📄 License

Proprietary - All rights reserved

## 🆘 Support

- **Documentation**: [docs/](./docs/)
- **Issues**: GitHub Issues
- **Support**: <EMAIL>
