using Neo4j.Driver;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;

namespace JimThread.Infrastructure.Data.Neo4j;

/// <summary>
/// Neo4j database context for managing graph database connections
/// </summary>
public class Neo4jContext : IDisposable
{
    private readonly IDriver _driver;
    private readonly ILogger<Neo4jContext> _logger;
    private readonly Neo4jOptions _options;

    public Neo4jContext(IOptions<Neo4jOptions> options, ILogger<Neo4jContext> logger)
    {
        _options = options.Value;
        _logger = logger;

        var config = ConfigBuilder.New()
            .WithEncryption(_options.Encrypted)
            .WithTrustStrategy(TrustStrategy.TrustAllCertificates)
            .WithConnectionTimeout(TimeSpan.FromSeconds(_options.ConnectionTimeoutSeconds))
            .WithMaxConnectionLifetime(TimeSpan.FromMinutes(_options.MaxConnectionLifetimeMinutes))
            .WithMaxConnectionPoolSize(_options.MaxConnectionPoolSize)
            .WithConnectionAcquisitionTimeout(TimeSpan.FromSeconds(_options.ConnectionAcquisitionTimeoutSeconds))
            .Build();

        _driver = GraphDatabase.Driver(_options.Uri, AuthTokens.Basic(_options.Username, _options.Password), config);

        _logger.LogInformation("Neo4j driver initialized for URI: {Uri}", _options.Uri);
    }

    /// <summary>
    /// Creates a new asynchronous session
    /// </summary>
    /// <param name="database">Database name (optional)</param>
    /// <param name="accessMode">Access mode (read/write)</param>
    /// <returns>Async session</returns>
    public IAsyncSession CreateAsyncSession(string? database = null, AccessMode accessMode = AccessMode.Write)
    {
        var sessionConfig = new SessionConfigBuilder();
        
        if (!string.IsNullOrEmpty(database))
        {
            sessionConfig = sessionConfig.WithDatabase(database);
        }

        sessionConfig = sessionConfig.WithDefaultAccessMode(accessMode);

        return _driver.AsyncSession(sessionConfig.Build());
    }

    /// <summary>
    /// Executes a read transaction
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="work">Work function to execute</param>
    /// <param name="database">Database name (optional)</param>
    /// <returns>Result of the transaction</returns>
    public async Task<T> ExecuteReadTransactionAsync<T>(Func<IAsyncQueryRunner, Task<T>> work, string? database = null)
    {
        await using var session = CreateAsyncSession(database, AccessMode.Read);
        return await session.ExecuteReadAsync(work);
    }

    /// <summary>
    /// Executes a write transaction
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="work">Work function to execute</param>
    /// <param name="database">Database name (optional)</param>
    /// <returns>Result of the transaction</returns>
    public async Task<T> ExecuteWriteTransactionAsync<T>(Func<IAsyncQueryRunner, Task<T>> work, string? database = null)
    {
        await using var session = CreateAsyncSession(database, AccessMode.Write);
        return await session.ExecuteWriteAsync(work);
    }

    /// <summary>
    /// Executes a write transaction without return value
    /// </summary>
    /// <param name="work">Work function to execute</param>
    /// <param name="database">Database name (optional)</param>
    public async Task ExecuteWriteTransactionAsync(Func<IAsyncQueryRunner, Task> work, string? database = null)
    {
        await using var session = CreateAsyncSession(database, AccessMode.Write);
        await session.ExecuteWriteAsync(work);
    }

    /// <summary>
    /// Verifies connectivity to the Neo4j database
    /// </summary>
    /// <returns>True if connection is successful</returns>
    public async Task<bool> VerifyConnectivityAsync()
    {
        try
        {
            await _driver.VerifyConnectivityAsync();
            _logger.LogInformation("Neo4j connectivity verified successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to verify Neo4j connectivity");
            return false;
        }
    }

    /// <summary>
    /// Gets database information
    /// </summary>
    /// <returns>Database information</returns>
    public async Task<Dictionary<string, object>> GetDatabaseInfoAsync()
    {
        return await ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync("CALL dbms.components() YIELD name, versions, edition");
            var records = await result.ToListAsync();
            
            var info = new Dictionary<string, object>();
            foreach (var record in records)
            {
                info[record["name"].As<string>()] = new
                {
                    Versions = record["versions"].As<List<object>>(),
                    Edition = record["edition"].As<string>()
                };
            }
            
            return info;
        });
    }

    /// <summary>
    /// Initializes the database schema with constraints and indexes
    /// </summary>
    public async Task InitializeSchemaAsync()
    {
        _logger.LogInformation("Initializing Neo4j schema...");

        await ExecuteWriteTransactionAsync(async tx =>
        {
            // Create constraints
            var constraints = new[]
            {
                "CREATE CONSTRAINT asset_id_unique IF NOT EXISTS FOR (a:Asset) REQUIRE a.id IS UNIQUE",
                "CREATE CONSTRAINT sensor_id_unique IF NOT EXISTS FOR (s:Sensor) REQUIRE s.id IS UNIQUE",
                "CREATE CONSTRAINT sensor_tag_unique IF NOT EXISTS FOR (s:Sensor) REQUIRE s.tag IS UNIQUE",
                "CREATE CONSTRAINT user_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.id IS UNIQUE"
            };

            foreach (var constraint in constraints)
            {
                try
                {
                    await tx.RunAsync(constraint);
                    _logger.LogDebug("Created constraint: {Constraint}", constraint);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to create constraint: {Constraint}", constraint);
                }
            }

            // Create indexes
            var indexes = new[]
            {
                "CREATE INDEX asset_name_index IF NOT EXISTS FOR (a:Asset) ON (a.name)",
                "CREATE INDEX asset_type_index IF NOT EXISTS FOR (a:Asset) ON (a.assetType)",
                "CREATE INDEX asset_status_index IF NOT EXISTS FOR (a:Asset) ON (a.status)",
                "CREATE INDEX sensor_name_index IF NOT EXISTS FOR (s:Sensor) ON (s.name)",
                "CREATE INDEX sensor_type_index IF NOT EXISTS FOR (s:Sensor) ON (s.sensorType)",
                "CREATE INDEX sensor_status_index IF NOT EXISTS FOR (s:Sensor) ON (s.status)",
                "CREATE INDEX event_timestamp_index IF NOT EXISTS FOR (e:Event) ON (e.timestamp)"
            };

            foreach (var index in indexes)
            {
                try
                {
                    await tx.RunAsync(index);
                    _logger.LogDebug("Created index: {Index}", index);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to create index: {Index}", index);
                }
            }
        });

        _logger.LogInformation("Neo4j schema initialization completed");
    }

    public void Dispose()
    {
        _driver?.Dispose();
        _logger.LogInformation("Neo4j driver disposed");
    }
}

/// <summary>
/// Configuration options for Neo4j connection
/// </summary>
public class Neo4jOptions
{
    public const string SectionName = "Neo4j";

    /// <summary>
    /// Neo4j connection URI (e.g., "bolt://localhost:7687")
    /// </summary>
    public string Uri { get; set; } = "bolt://localhost:7687";

    /// <summary>
    /// Username for authentication
    /// </summary>
    public string Username { get; set; } = "neo4j";

    /// <summary>
    /// Password for authentication
    /// </summary>
    public string Password { get; set; } = "password";

    /// <summary>
    /// Whether to use encrypted connections
    /// </summary>
    public bool Encrypted { get; set; } = false;

    /// <summary>
    /// Connection timeout in seconds
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Maximum connection lifetime in minutes
    /// </summary>
    public int MaxConnectionLifetimeMinutes { get; set; } = 60;

    /// <summary>
    /// Maximum connection pool size
    /// </summary>
    public int MaxConnectionPoolSize { get; set; } = 100;

    /// <summary>
    /// Connection acquisition timeout in seconds
    /// </summary>
    public int ConnectionAcquisitionTimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// Default database name
    /// </summary>
    public string? Database { get; set; }
}
