using System.Linq.Expressions;
using JimThread.Core.Domain.Entities;
using JimThread.Core.Interfaces;
using JimThread.Infrastructure.Data.Neo4j;
using Microsoft.Extensions.Logging;
using Neo4j.Driver;
using Newtonsoft.Json;

namespace JimThread.Infrastructure.Data.Repositories;

/// <summary>
/// Neo4j implementation of the Asset repository
/// </summary>
public class Neo4jAssetRepository : IAssetRepository
{
    private readonly Neo4jContext _context;
    private readonly ILogger<Neo4jAssetRepository> _logger;

    public Neo4jAssetRepository(Neo4jContext context, ILogger<Neo4jAssetRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Asset?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(
                "MATCH (a:Asset {id: $id}) WHERE NOT a.isDeleted RETURN a",
                new { id = id.ToString() });

            var record = await result.SingleOrDefaultAsync();
            return record != null ? MapToAsset(record["a"].As<INode>()) : null;
        });
    }

    public async Task<IEnumerable<Asset>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync("MATCH (a:Asset) WHERE NOT a.isDeleted RETURN a ORDER BY a.name");
            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> FindAsync(Expression<Func<Asset, bool>> predicate, CancellationToken cancellationToken = default)
    {
        // For now, we'll implement basic filtering. In a production system, you'd want to translate
        // the expression to Cypher query parameters
        var allAssets = await GetAllAsync(cancellationToken);
        return allAssets.Where(predicate.Compile());
    }

    public async Task<Asset?> SingleOrDefaultAsync(Expression<Func<Asset, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var assets = await FindAsync(predicate, cancellationToken);
        return assets.SingleOrDefault();
    }

    public async Task<Asset> AddAsync(Asset entity, CancellationToken cancellationToken = default)
    {
        entity.Id = entity.Id == Guid.Empty ? Guid.NewGuid() : entity.Id;
        entity.CreatedAt = DateTimeOffset.UtcNow;
        entity.UpdatedAt = DateTimeOffset.UtcNow;

        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var parameters = MapToParameters(entity);
            
            var result = await tx.RunAsync(@"
                CREATE (a:Asset $properties)
                RETURN a", new { properties = parameters });

            var record = await result.SingleAsync();
            return MapToAsset(record["a"].As<INode>());
        });
    }

    public async Task<IEnumerable<Asset>> AddRangeAsync(IEnumerable<Asset> entities, CancellationToken cancellationToken = default)
    {
        var assets = entities.ToList();
        var results = new List<Asset>();

        foreach (var asset in assets)
        {
            results.Add(await AddAsync(asset, cancellationToken));
        }

        return results;
    }

    public async Task<Asset> UpdateAsync(Asset entity, CancellationToken cancellationToken = default)
    {
        entity.UpdatedAt = DateTimeOffset.UtcNow;
        entity.Version++;

        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var parameters = MapToParameters(entity);
            
            var result = await tx.RunAsync(@"
                MATCH (a:Asset {id: $id})
                SET a += $properties
                RETURN a", new { id = entity.Id.ToString(), properties = parameters });

            var record = await result.SingleAsync();
            return MapToAsset(record["a"].As<INode>());
        });
    }

    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset {id: $id})
                DETACH DELETE a
                RETURN count(a) as deletedCount", new { id = id.ToString() });

            var record = await result.SingleAsync();
            return record["deletedCount"].As<int>() > 0;
        });
    }

    public async Task<bool> SoftDeleteAsync(Guid id, string? deletedBy = null, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset {id: $id})
                SET a.isDeleted = true, a.deletedAt = $deletedAt, a.deletedBy = $deletedBy
                RETURN count(a) as updatedCount", 
                new { 
                    id = id.ToString(), 
                    deletedAt = DateTimeOffset.UtcNow.ToString("O"),
                    deletedBy = deletedBy 
                });

            var record = await result.SingleAsync();
            return record["updatedCount"].As<int>() > 0;
        });
    }

    public async Task<bool> ExistsAsync(Expression<Func<Asset, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var asset = await SingleOrDefaultAsync(predicate, cancellationToken);
        return asset != null;
    }

    public async Task<int> CountAsync(Expression<Func<Asset, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        if (predicate == null)
        {
            return await _context.ExecuteReadTransactionAsync(async tx =>
            {
                var result = await tx.RunAsync("MATCH (a:Asset) WHERE NOT a.isDeleted RETURN count(a) as count");
                var record = await result.SingleAsync();
                return record["count"].As<int>();
            });
        }

        var assets = await FindAsync(predicate, cancellationToken);
        return assets.Count();
    }

    public async Task<PagedResult<Asset>> GetPagedAsync(int pageNumber, int pageSize, Expression<Func<Asset, bool>>? predicate = null, Expression<Func<Asset, object>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default)
    {
        var skip = (pageNumber - 1) * pageSize;
        
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            // Get total count
            var countResult = await tx.RunAsync("MATCH (a:Asset) WHERE NOT a.isDeleted RETURN count(a) as totalCount");
            var countRecord = await countResult.SingleAsync();
            var totalCount = countRecord["totalCount"].As<int>();

            // Get paged data
            var dataResult = await tx.RunAsync(@"
                MATCH (a:Asset) 
                WHERE NOT a.isDeleted 
                RETURN a 
                ORDER BY a.name 
                SKIP $skip 
                LIMIT $limit", 
                new { skip, limit = pageSize });

            var records = await dataResult.ToListAsync();
            var items = records.Select(record => MapToAsset(record["a"].As<INode>()));

            return new PagedResult<Asset>
            {
                Items = items,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalCount = totalCount
            };
        });
    }

    // Asset-specific methods

    public async Task<IEnumerable<Asset>> GetByTypeAsync(string assetType, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset {assetType: $assetType}) 
                WHERE NOT a.isDeleted 
                RETURN a 
                ORDER BY a.name", 
                new { assetType });

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> GetByStatusAsync(AssetStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset {status: $status}) 
                WHERE NOT a.isDeleted 
                RETURN a 
                ORDER BY a.name", 
                new { status = status.ToString() });

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> GetChildAssetsAsync(Guid parentAssetId, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (parent:Asset {id: $parentId})-[:HAS_CHILD]->(child:Asset)
                WHERE NOT child.isDeleted
                RETURN child
                ORDER BY child.name", 
                new { parentId = parentAssetId.ToString() });

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["child"].As<INode>()));
        });
    }

    public async Task<Asset?> GetAssetHierarchyAsync(Guid rootAssetId, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (root:Asset {id: $rootId})
                WHERE NOT root.isDeleted
                OPTIONAL MATCH (root)-[:HAS_CHILD*]->(descendant:Asset)
                WHERE NOT descendant.isDeleted
                RETURN root, collect(descendant) as descendants", 
                new { rootId = rootAssetId.ToString() });

            var record = await result.SingleOrDefaultAsync();
            if (record == null) return null;

            var rootAsset = MapToAsset(record["root"].As<INode>());
            // Note: In a full implementation, you'd build the complete hierarchy tree
            // For now, we're just returning the root asset
            return rootAsset;
        });
    }

    public async Task<IEnumerable<Asset>> GetRootAssetsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset)
                WHERE NOT a.isDeleted AND a.parentAssetId IS NULL
                RETURN a
                ORDER BY a.name");

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> GetByLocationAsync(string? building = null, string? floor = null, string? room = null, CancellationToken cancellationToken = default)
    {
        var whereConditions = new List<string> { "NOT a.isDeleted" };
        var parameters = new Dictionary<string, object>();

        if (!string.IsNullOrEmpty(building))
        {
            whereConditions.Add("a.location_building = $building");
            parameters["building"] = building;
        }

        if (!string.IsNullOrEmpty(floor))
        {
            whereConditions.Add("a.location_floor = $floor");
            parameters["floor"] = floor;
        }

        if (!string.IsNullOrEmpty(room))
        {
            whereConditions.Add("a.location_room = $room");
            parameters["room"] = room;
        }

        var whereClause = string.Join(" AND ", whereConditions);

        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync($@"
                MATCH (a:Asset)
                WHERE {whereClause}
                RETURN a
                ORDER BY a.name", parameters);

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> GetByCriticalityAsync(int criticalityLevel, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset {criticalityLevel: $criticalityLevel})
                WHERE NOT a.isDeleted
                RETURN a
                ORDER BY a.name", 
                new { criticalityLevel });

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> GetAssetsRequiringMaintenanceAsync(int daysAhead = 30, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTimeOffset.UtcNow.AddDays(daysAhead);

        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset)
                WHERE NOT a.isDeleted 
                AND (a.nextMaintenanceDate <= $cutoffDate OR a.nextMaintenanceDate < $now)
                RETURN a
                ORDER BY a.nextMaintenanceDate", 
                new { 
                    cutoffDate = cutoffDate.ToString("O"),
                    now = DateTimeOffset.UtcNow.ToString("O")
                });

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset)
                WHERE NOT a.isDeleted 
                AND (toLower(a.name) CONTAINS toLower($searchTerm) 
                     OR toLower(a.description) CONTAINS toLower($searchTerm)
                     OR any(tag IN a.tags WHERE toLower(tag) CONTAINS toLower($searchTerm)))
                RETURN a
                ORDER BY a.name", 
                new { searchTerm });

            var records = await result.ToListAsync();
            return records.Select(record => MapToAsset(record["a"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Asset>> GetWithSensorsAsync(IEnumerable<Guid>? assetIds = null, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            string query;
            object parameters;

            if (assetIds != null)
            {
                var idStrings = assetIds.Select(id => id.ToString()).ToList();
                query = @"
                    MATCH (a:Asset)
                    WHERE NOT a.isDeleted AND a.id IN $assetIds
                    OPTIONAL MATCH (a)-[:HAS_SENSOR]->(s:Sensor)
                    WHERE NOT s.isDeleted
                    RETURN a, collect(s) as sensors
                    ORDER BY a.name";
                parameters = new { assetIds = idStrings };
            }
            else
            {
                query = @"
                    MATCH (a:Asset)
                    WHERE NOT a.isDeleted
                    OPTIONAL MATCH (a)-[:HAS_SENSOR]->(s:Sensor)
                    WHERE NOT s.isDeleted
                    RETURN a, collect(s) as sensors
                    ORDER BY a.name";
                parameters = new { };
            }

            var result = await tx.RunAsync(query, parameters);
            var records = await result.ToListAsync();
            
            return records.Select(record => 
            {
                var asset = MapToAsset(record["a"].As<INode>());
                // Note: In a full implementation, you'd map the sensors too
                return asset;
            });
        });
    }

    public async Task<int> UpdateStatusAsync(IEnumerable<Guid> assetIds, AssetStatus status, string? updatedBy = null, CancellationToken cancellationToken = default)
    {
        var idStrings = assetIds.Select(id => id.ToString()).ToList();

        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset)
                WHERE a.id IN $assetIds
                SET a.status = $status, a.updatedAt = $updatedAt, a.updatedBy = $updatedBy
                RETURN count(a) as updatedCount", 
                new { 
                    assetIds = idStrings,
                    status = status.ToString(),
                    updatedAt = DateTimeOffset.UtcNow.ToString("O"),
                    updatedBy = updatedBy
                });

            var record = await result.SingleAsync();
            return record["updatedCount"].As<int>();
        });
    }

    public async Task<Dictionary<string, int>> GetAssetStatisticsByTypeAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset)
                WHERE NOT a.isDeleted
                RETURN a.assetType as type, count(a) as count
                ORDER BY type");

            var records = await result.ToListAsync();
            return records.ToDictionary(
                record => record["type"].As<string>(),
                record => record["count"].As<int>());
        });
    }

    public async Task<Dictionary<AssetStatus, int>> GetAssetStatisticsByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (a:Asset)
                WHERE NOT a.isDeleted
                RETURN a.status as status, count(a) as count
                ORDER BY status");

            var records = await result.ToListAsync();
            return records.ToDictionary(
                record => Enum.Parse<AssetStatus>(record["status"].As<string>()),
                record => record["count"].As<int>());
        });
    }

    private static Asset MapToAsset(INode node)
    {
        var properties = node.Properties;
        
        return new Asset
        {
            Id = Guid.Parse(properties["id"].As<string>()),
            Name = properties["name"].As<string>(),
            Description = properties.GetValueOrDefault("description")?.As<string>(),
            AssetType = properties["assetType"].As<string>(),
            Manufacturer = properties.GetValueOrDefault("manufacturer")?.As<string>(),
            Model = properties.GetValueOrDefault("model")?.As<string>(),
            SerialNumber = properties.GetValueOrDefault("serialNumber")?.As<string>(),
            Status = Enum.Parse<AssetStatus>(properties.GetValueOrDefault("status")?.As<string>() ?? "Unknown"),
            ParentAssetId = properties.GetValueOrDefault("parentAssetId")?.As<string>() != null 
                ? Guid.Parse(properties["parentAssetId"].As<string>()) 
                : null,
            CriticalityLevel = properties.GetValueOrDefault("criticalityLevel")?.As<int>() ?? 3,
            CreatedAt = DateTimeOffset.Parse(properties["createdAt"].As<string>()),
            UpdatedAt = DateTimeOffset.Parse(properties["updatedAt"].As<string>()),
            CreatedBy = properties.GetValueOrDefault("createdBy")?.As<string>(),
            UpdatedBy = properties.GetValueOrDefault("updatedBy")?.As<string>(),
            IsDeleted = properties.GetValueOrDefault("isDeleted")?.As<bool>() ?? false,
            Version = properties.GetValueOrDefault("version")?.As<long>() ?? 1
        };
    }

    private static Dictionary<string, object> MapToParameters(Asset asset)
    {
        return new Dictionary<string, object>
        {
            ["id"] = asset.Id.ToString(),
            ["name"] = asset.Name,
            ["description"] = asset.Description ?? "",
            ["assetType"] = asset.AssetType,
            ["manufacturer"] = asset.Manufacturer ?? "",
            ["model"] = asset.Model ?? "",
            ["serialNumber"] = asset.SerialNumber ?? "",
            ["status"] = asset.Status.ToString(),
            ["parentAssetId"] = asset.ParentAssetId?.ToString() ?? "",
            ["criticalityLevel"] = asset.CriticalityLevel,
            ["createdAt"] = asset.CreatedAt.ToString("O"),
            ["updatedAt"] = asset.UpdatedAt.ToString("O"),
            ["createdBy"] = asset.CreatedBy ?? "",
            ["updatedBy"] = asset.UpdatedBy ?? "",
            ["isDeleted"] = asset.IsDeleted,
            ["version"] = asset.Version
        };
    }
}
