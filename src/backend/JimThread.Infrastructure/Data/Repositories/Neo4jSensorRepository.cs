using System.Linq.Expressions;
using JimThread.Core.Domain.Entities;
using JimThread.Core.Interfaces;
using JimThread.Infrastructure.Data.Neo4j;
using Microsoft.Extensions.Logging;
using Neo4j.Driver;

namespace JimThread.Infrastructure.Data.Repositories;

/// <summary>
/// Neo4j implementation of the Sensor repository
/// </summary>
public class Neo4jSensorRepository : ISensorRepository
{
    private readonly Neo4jContext _context;
    private readonly ILogger<Neo4jSensorRepository> _logger;

    public Neo4jSensorRepository(Neo4jContext context, ILogger<Neo4jSensorRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Sensor?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(
                "MATCH (s:Sensor {id: $id}) WHERE NOT s.isDeleted RETURN s",
                new { id = id.ToString() });

            var record = await result.SingleOrDefaultAsync();
            return record != null ? MapToSensor(record["s"].As<INode>()) : null;
        });
    }

    public async Task<IEnumerable<Sensor>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync("MATCH (s:Sensor) WHERE NOT s.isDeleted RETURN s ORDER BY s.tag");
            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> FindAsync(Expression<Func<Sensor, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var allSensors = await GetAllAsync(cancellationToken);
        return allSensors.Where(predicate.Compile());
    }

    public async Task<Sensor?> SingleOrDefaultAsync(Expression<Func<Sensor, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var sensors = await FindAsync(predicate, cancellationToken);
        return sensors.SingleOrDefault();
    }

    public async Task<Sensor> AddAsync(Sensor entity, CancellationToken cancellationToken = default)
    {
        entity.Id = entity.Id == Guid.Empty ? Guid.NewGuid() : entity.Id;
        entity.CreatedAt = DateTimeOffset.UtcNow;
        entity.UpdatedAt = DateTimeOffset.UtcNow;

        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var parameters = MapToParameters(entity);
            
            var result = await tx.RunAsync(@"
                CREATE (s:Sensor $properties)
                RETURN s", new { properties = parameters });

            var record = await result.SingleAsync();
            return MapToSensor(record["s"].As<INode>());
        });
    }

    public async Task<IEnumerable<Sensor>> AddRangeAsync(IEnumerable<Sensor> entities, CancellationToken cancellationToken = default)
    {
        var sensors = entities.ToList();
        var results = new List<Sensor>();

        foreach (var sensor in sensors)
        {
            results.Add(await AddAsync(sensor, cancellationToken));
        }

        return results;
    }

    public async Task<Sensor> UpdateAsync(Sensor entity, CancellationToken cancellationToken = default)
    {
        entity.UpdatedAt = DateTimeOffset.UtcNow;
        entity.Version++;

        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var parameters = MapToParameters(entity);
            
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {id: $id})
                SET s += $properties
                RETURN s", new { id = entity.Id.ToString(), properties = parameters });

            var record = await result.SingleAsync();
            return MapToSensor(record["s"].As<INode>());
        });
    }

    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {id: $id})
                DETACH DELETE s
                RETURN count(s) as deletedCount", new { id = id.ToString() });

            var record = await result.SingleAsync();
            return record["deletedCount"].As<int>() > 0;
        });
    }

    public async Task<bool> SoftDeleteAsync(Guid id, string? deletedBy = null, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {id: $id})
                SET s.isDeleted = true, s.deletedAt = $deletedAt, s.deletedBy = $deletedBy
                RETURN count(s) as updatedCount", 
                new { 
                    id = id.ToString(), 
                    deletedAt = DateTimeOffset.UtcNow.ToString("O"),
                    deletedBy = deletedBy 
                });

            var record = await result.SingleAsync();
            return record["updatedCount"].As<int>() > 0;
        });
    }

    public async Task<bool> ExistsAsync(Expression<Func<Sensor, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var sensor = await SingleOrDefaultAsync(predicate, cancellationToken);
        return sensor != null;
    }

    public async Task<int> CountAsync(Expression<Func<Sensor, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        if (predicate == null)
        {
            return await _context.ExecuteReadTransactionAsync(async tx =>
            {
                var result = await tx.RunAsync("MATCH (s:Sensor) WHERE NOT s.isDeleted RETURN count(s) as count");
                var record = await result.SingleAsync();
                return record["count"].As<int>();
            });
        }

        var sensors = await FindAsync(predicate, cancellationToken);
        return sensors.Count();
    }

    public async Task<PagedResult<Sensor>> GetPagedAsync(int pageNumber, int pageSize, Expression<Func<Sensor, bool>>? predicate = null, Expression<Func<Sensor, object>>? orderBy = null, bool ascending = true, CancellationToken cancellationToken = default)
    {
        var skip = (pageNumber - 1) * pageSize;
        
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var countResult = await tx.RunAsync("MATCH (s:Sensor) WHERE NOT s.isDeleted RETURN count(s) as totalCount");
            var countRecord = await countResult.SingleAsync();
            var totalCount = countRecord["totalCount"].As<int>();

            var dataResult = await tx.RunAsync(@"
                MATCH (s:Sensor) 
                WHERE NOT s.isDeleted 
                RETURN s 
                ORDER BY s.tag 
                SKIP $skip 
                LIMIT $limit", 
                new { skip, limit = pageSize });

            var records = await dataResult.ToListAsync();
            var items = records.Select(record => MapToSensor(record["s"].As<INode>()));

            return new PagedResult<Sensor>
            {
                Items = items,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalCount = totalCount
            };
        });
    }

    // Sensor-specific methods

    public async Task<IEnumerable<Sensor>> GetByAssetIdAsync(Guid assetId, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {assetId: $assetId}) 
                WHERE NOT s.isDeleted 
                RETURN s 
                ORDER BY s.tag", 
                new { assetId = assetId.ToString() });

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> GetByTypeAsync(string sensorType, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {sensorType: $sensorType}) 
                WHERE NOT s.isDeleted 
                RETURN s 
                ORDER BY s.tag", 
                new { sensorType });

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> GetByStatusAsync(SensorStatus status, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {status: $status}) 
                WHERE NOT s.isDeleted 
                RETURN s 
                ORDER BY s.tag", 
                new { status = status.ToString() });

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<Sensor?> GetByTagAsync(string tag, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {tag: $tag}) 
                WHERE NOT s.isDeleted 
                RETURN s", 
                new { tag });

            var record = await result.SingleOrDefaultAsync();
            return record != null ? MapToSensor(record["s"].As<INode>()) : null;
        });
    }

    public async Task<IEnumerable<Sensor>> GetByTagsAsync(IEnumerable<string> tags, CancellationToken cancellationToken = default)
    {
        var tagList = tags.ToList();
        
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor) 
                WHERE NOT s.isDeleted AND s.tag IN $tags
                RETURN s 
                ORDER BY s.tag", 
                new { tags = tagList });

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> GetAnomalyDetectionEnabledAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {enableAnomalyDetection: true}) 
                WHERE NOT s.isDeleted 
                RETURN s 
                ORDER BY s.tag");

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> GetStaleSensorsAsync(DateTimeOffset timeThreshold, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor) 
                WHERE NOT s.isDeleted 
                AND (s.lastReadingAt IS NULL OR s.lastReadingAt < $threshold)
                RETURN s 
                ORDER BY s.lastReadingAt", 
                new { threshold = timeThreshold.ToString("O") });

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> GetSensorsWithAlarmsAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor) 
                WHERE NOT s.isDeleted 
                AND s.alarmConfiguration_alarmsEnabled = true
                RETURN s 
                ORDER BY s.tag");

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> GetByDataSourceTypeAsync(string sourceType, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor) 
                WHERE NOT s.isDeleted 
                AND s.dataSource_sourceType = $sourceType
                RETURN s 
                ORDER BY s.tag", 
                new { sourceType });

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<bool> UpdateLastReadingAsync(Guid sensorId, double value, DateTimeOffset timestamp, DataQuality quality, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor {id: $sensorId})
                SET s.lastValue = $value, 
                    s.lastReadingAt = $timestamp, 
                    s.lastReadingQuality = $quality,
                    s.updatedAt = $updatedAt
                RETURN count(s) as updatedCount", 
                new { 
                    sensorId = sensorId.ToString(),
                    value = value,
                    timestamp = timestamp.ToString("O"),
                    quality = quality.ToString(),
                    updatedAt = DateTimeOffset.UtcNow.ToString("O")
                });

            var record = await result.SingleAsync();
            return record["updatedCount"].As<int>() > 0;
        });
    }

    public async Task<int> UpdateStatusAsync(IEnumerable<Guid> sensorIds, SensorStatus status, string? updatedBy = null, CancellationToken cancellationToken = default)
    {
        var idStrings = sensorIds.Select(id => id.ToString()).ToList();

        return await _context.ExecuteWriteTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor)
                WHERE s.id IN $sensorIds
                SET s.status = $status, s.updatedAt = $updatedAt, s.updatedBy = $updatedBy
                RETURN count(s) as updatedCount", 
                new { 
                    sensorIds = idStrings,
                    status = status.ToString(),
                    updatedAt = DateTimeOffset.UtcNow.ToString("O"),
                    updatedBy = updatedBy
                });

            var record = await result.SingleAsync();
            return record["updatedCount"].As<int>();
        });
    }

    public async Task<IEnumerable<Sensor>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor)
                WHERE NOT s.isDeleted 
                AND (toLower(s.tag) CONTAINS toLower($searchTerm) 
                     OR toLower(s.name) CONTAINS toLower($searchTerm)
                     OR toLower(s.description) CONTAINS toLower($searchTerm))
                RETURN s
                ORDER BY s.tag", 
                new { searchTerm });

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<Dictionary<string, int>> GetSensorStatisticsByTypeAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor)
                WHERE NOT s.isDeleted
                RETURN s.sensorType as type, count(s) as count
                ORDER BY type");

            var records = await result.ToListAsync();
            return records.ToDictionary(
                record => record["type"].As<string>(),
                record => record["count"].As<int>());
        });
    }

    public async Task<Dictionary<SensorStatus, int>> GetSensorStatisticsByStatusAsync(CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor)
                WHERE NOT s.isDeleted
                RETURN s.status as status, count(s) as count
                ORDER BY status");

            var records = await result.ToListAsync();
            return records.ToDictionary(
                record => Enum.Parse<SensorStatus>(record["status"].As<string>()),
                record => record["count"].As<int>());
        });
    }

    public async Task<IEnumerable<Sensor>> GetSensorsDueForCalibrationAsync(int daysAhead = 30, CancellationToken cancellationToken = default)
    {
        // This is a placeholder implementation - in a real system, you'd have calibration scheduling
        var cutoffDate = DateTimeOffset.UtcNow.AddDays(daysAhead);

        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            var result = await tx.RunAsync(@"
                MATCH (s:Sensor)
                WHERE NOT s.isDeleted 
                AND s.status = 'Calibration'
                RETURN s
                ORDER BY s.tag");

            var records = await result.ToListAsync();
            return records.Select(record => MapToSensor(record["s"].As<INode>()));
        });
    }

    public async Task<IEnumerable<Sensor>> GetWithAssetsAsync(IEnumerable<Guid>? sensorIds = null, CancellationToken cancellationToken = default)
    {
        return await _context.ExecuteReadTransactionAsync(async tx =>
        {
            string query;
            object parameters;

            if (sensorIds != null)
            {
                var idStrings = sensorIds.Select(id => id.ToString()).ToList();
                query = @"
                    MATCH (s:Sensor)
                    WHERE NOT s.isDeleted AND s.id IN $sensorIds
                    OPTIONAL MATCH (a:Asset {id: s.assetId})
                    WHERE NOT a.isDeleted
                    RETURN s, a
                    ORDER BY s.tag";
                parameters = new { sensorIds = idStrings };
            }
            else
            {
                query = @"
                    MATCH (s:Sensor)
                    WHERE NOT s.isDeleted
                    OPTIONAL MATCH (a:Asset {id: s.assetId})
                    WHERE NOT a.isDeleted
                    RETURN s, a
                    ORDER BY s.tag";
                parameters = new { };
            }

            var result = await tx.RunAsync(query, parameters);
            var records = await result.ToListAsync();
            
            return records.Select(record => 
            {
                var sensor = MapToSensor(record["s"].As<INode>());
                // Note: In a full implementation, you'd also map the asset
                return sensor;
            });
        });
    }

    private static Sensor MapToSensor(INode node)
    {
        var properties = node.Properties;
        
        return new Sensor
        {
            Id = Guid.Parse(properties["id"].As<string>()),
            Tag = properties["tag"].As<string>(),
            Name = properties["name"].As<string>(),
            Description = properties.GetValueOrDefault("description")?.As<string>(),
            SensorType = properties["sensorType"].As<string>(),
            Unit = properties["unit"].As<string>(),
            DataType = Enum.Parse<SensorDataType>(properties.GetValueOrDefault("dataType")?.As<string>() ?? "Numeric"),
            MinValue = properties.GetValueOrDefault("minValue")?.As<double?>(),
            MaxValue = properties.GetValueOrDefault("maxValue")?.As<double?>(),
            Precision = properties.GetValueOrDefault("precision")?.As<int>() ?? 2,
            SamplingRateSeconds = properties.GetValueOrDefault("samplingRateSeconds")?.As<int>() ?? 60,
            AssetId = Guid.Parse(properties["assetId"].As<string>()),
            Status = Enum.Parse<SensorStatus>(properties.GetValueOrDefault("status")?.As<string>() ?? "Unknown"),
            LastValue = properties.GetValueOrDefault("lastValue")?.As<double?>(),
            LastReadingAt = properties.GetValueOrDefault("lastReadingAt")?.As<string>() != null 
                ? DateTimeOffset.Parse(properties["lastReadingAt"].As<string>()) 
                : null,
            LastReadingQuality = Enum.Parse<DataQuality>(properties.GetValueOrDefault("lastReadingQuality")?.As<string>() ?? "Unknown"),
            EnableAnomalyDetection = properties.GetValueOrDefault("enableAnomalyDetection")?.As<bool>() ?? true,
            EnableArchiving = properties.GetValueOrDefault("enableArchiving")?.As<bool>() ?? true,
            RetentionDays = properties.GetValueOrDefault("retentionDays")?.As<int>() ?? 365,
            CreatedAt = DateTimeOffset.Parse(properties["createdAt"].As<string>()),
            UpdatedAt = DateTimeOffset.Parse(properties["updatedAt"].As<string>()),
            CreatedBy = properties.GetValueOrDefault("createdBy")?.As<string>(),
            UpdatedBy = properties.GetValueOrDefault("updatedBy")?.As<string>(),
            IsDeleted = properties.GetValueOrDefault("isDeleted")?.As<bool>() ?? false,
            Version = properties.GetValueOrDefault("version")?.As<long>() ?? 1
        };
    }

    private static Dictionary<string, object> MapToParameters(Sensor sensor)
    {
        return new Dictionary<string, object>
        {
            ["id"] = sensor.Id.ToString(),
            ["tag"] = sensor.Tag,
            ["name"] = sensor.Name,
            ["description"] = sensor.Description ?? "",
            ["sensorType"] = sensor.SensorType,
            ["unit"] = sensor.Unit,
            ["dataType"] = sensor.DataType.ToString(),
            ["minValue"] = sensor.MinValue ?? 0.0,
            ["maxValue"] = sensor.MaxValue ?? 0.0,
            ["precision"] = sensor.Precision,
            ["samplingRateSeconds"] = sensor.SamplingRateSeconds,
            ["assetId"] = sensor.AssetId.ToString(),
            ["status"] = sensor.Status.ToString(),
            ["lastValue"] = sensor.LastValue ?? 0.0,
            ["lastReadingAt"] = sensor.LastReadingAt?.ToString("O") ?? "",
            ["lastReadingQuality"] = sensor.LastReadingQuality.ToString(),
            ["enableAnomalyDetection"] = sensor.EnableAnomalyDetection,
            ["enableArchiving"] = sensor.EnableArchiving,
            ["retentionDays"] = sensor.RetentionDays,
            ["createdAt"] = sensor.CreatedAt.ToString("O"),
            ["updatedAt"] = sensor.UpdatedAt.ToString("O"),
            ["createdBy"] = sensor.CreatedBy ?? "",
            ["updatedBy"] = sensor.UpdatedBy ?? "",
            ["isDeleted"] = sensor.IsDeleted,
            ["version"] = sensor.Version
        };
    }
}
