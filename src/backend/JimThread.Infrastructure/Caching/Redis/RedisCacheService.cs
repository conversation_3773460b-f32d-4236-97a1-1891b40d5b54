using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;

namespace JimThread.Infrastructure.Caching.Redis;

/// <summary>
/// Redis-based caching service with advanced features
/// </summary>
public class RedisCacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly IDatabase _database;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(
        IDistributedCache distributedCache,
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<RedisCacheService> logger)
    {
        _distributedCache = distributedCache;
        _database = connectionMultiplexer.GetDatabase();
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// Gets a cached value by key
    /// </summary>
    /// <typeparam name="T">Type of cached value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached value or default</returns>
    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedValue))
            {
                _logger.LogDebug("Cache miss for key: {Key}", key);
                return default;
            }

            _logger.LogDebug("Cache hit for key: {Key}", key);
            return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
            return default;
        }
    }

    /// <summary>
    /// Sets a cached value with expiration
    /// </summary>
    /// <typeparam name="T">Type of value to cache</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to cache</param>
    /// <param name="expiration">Expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            
            var options = new DistributedCacheEntryOptions();
            if (expiration.HasValue)
            {
                options.SetAbsoluteExpiration(expiration.Value);
            }

            await _distributedCache.SetStringAsync(key, serializedValue, options, cancellationToken);
            _logger.LogDebug("Cached value for key: {Key} with expiration: {Expiration}", key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
        }
    }

    /// <summary>
    /// Removes a cached value
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _distributedCache.RemoveAsync(key, cancellationToken);
            _logger.LogDebug("Removed cached value for key: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
        }
    }

    /// <summary>
    /// Gets or sets a cached value using a factory function
    /// </summary>
    /// <typeparam name="T">Type of cached value</typeparam>
    /// <param name="key">Cache key</param>
    /// <param name="factory">Factory function to create value if not cached</param>
    /// <param name="expiration">Expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached or newly created value</returns>
    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);
        
        if (cachedValue != null)
        {
            return cachedValue;
        }

        try
        {
            var newValue = await factory();
            if (newValue != null)
            {
                await SetAsync(key, newValue, expiration, cancellationToken);
            }
            return newValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in factory function for key: {Key}", key);
            return default;
        }
    }

    /// <summary>
    /// Checks if a key exists in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if key exists</returns>
    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyExistsAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if key exists: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Sets expiration for an existing key
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="expiration">Expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task<bool> ExpireAsync(string key, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyExpireAsync(key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting expiration for key: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Gets multiple cached values by keys
    /// </summary>
    /// <typeparam name="T">Type of cached values</typeparam>
    /// <param name="keys">Cache keys</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of key-value pairs</returns>
    public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default)
    {
        var result = new Dictionary<string, T?>();
        var keyList = keys.ToList();

        try
        {
            var redisKeys = keyList.Select(k => (RedisKey)k).ToArray();
            var values = await _database.StringGetAsync(redisKeys);

            for (int i = 0; i < keyList.Count; i++)
            {
                var key = keyList[i];
                var value = values[i];

                if (value.HasValue)
                {
                    try
                    {
                        result[key] = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, "Error deserializing cached value for key: {Key}", key);
                        result[key] = default;
                    }
                }
                else
                {
                    result[key] = default;
                }
            }

            _logger.LogDebug("Retrieved {Count} cached values", keyList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple cached values");
            
            // Return default values for all keys
            foreach (var key in keyList)
            {
                result[key] = default;
            }
        }

        return result;
    }

    /// <summary>
    /// Sets multiple cached values
    /// </summary>
    /// <typeparam name="T">Type of values to cache</typeparam>
    /// <param name="keyValuePairs">Key-value pairs to cache</param>
    /// <param name="expiration">Expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var tasks = keyValuePairs.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiration, cancellationToken));
            await Task.WhenAll(tasks);
            
            _logger.LogDebug("Set {Count} cached values", keyValuePairs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting multiple cached values");
        }
    }

    /// <summary>
    /// Removes multiple cached values
    /// </summary>
    /// <param name="keys">Cache keys to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task RemoveManyAsync(IEnumerable<string> keys, CancellationToken cancellationToken = default)
    {
        try
        {
            var keyList = keys.ToList();
            var redisKeys = keyList.Select(k => (RedisKey)k).ToArray();
            
            await _database.KeyDeleteAsync(redisKeys);
            _logger.LogDebug("Removed {Count} cached values", keyList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing multiple cached values");
        }
    }

    /// <summary>
    /// Removes all cached values matching a pattern
    /// </summary>
    /// <param name="pattern">Key pattern (supports wildcards)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern).ToArray();
            
            if (keys.Length > 0)
            {
                await _database.KeyDeleteAsync(keys);
                _logger.LogDebug("Removed {Count} cached values matching pattern: {Pattern}", keys.Length, pattern);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
        }
    }

    /// <summary>
    /// Increments a numeric value in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to increment by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New value after increment</returns>
    public async Task<long> IncrementAsync(string key, long value = 1, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.StringIncrementAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing value for key: {Key}", key);
            return 0;
        }
    }

    /// <summary>
    /// Decrements a numeric value in cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to decrement by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New value after decrement</returns>
    public async Task<long> DecrementAsync(string key, long value = 1, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.StringDecrementAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrementing value for key: {Key}", key);
            return 0;
        }
    }

    /// <summary>
    /// Gets cache statistics
    /// </summary>
    /// <returns>Cache statistics</returns>
    public async Task<CacheStatistics> GetStatisticsAsync()
    {
        try
        {
            var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
            var info = await server.InfoAsync();
            
            var stats = new CacheStatistics();
            
            foreach (var section in info)
            {
                foreach (var item in section)
                {
                    switch (item.Key.ToLower())
                    {
                        case "used_memory":
                            if (long.TryParse(item.Value, out var usedMemory))
                                stats.UsedMemoryBytes = usedMemory;
                            break;
                        case "keyspace_hits":
                            if (long.TryParse(item.Value, out var hits))
                                stats.CacheHits = hits;
                            break;
                        case "keyspace_misses":
                            if (long.TryParse(item.Value, out var misses))
                                stats.CacheMisses = misses;
                            break;
                        case "connected_clients":
                            if (int.TryParse(item.Value, out var clients))
                                stats.ConnectedClients = clients;
                            break;
                    }
                }
            }

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return new CacheStatistics();
        }
    }
}

/// <summary>
/// Cache service interface
/// </summary>
public interface ICacheService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
    Task<bool> ExpireAsync(string key, TimeSpan expiration, CancellationToken cancellationToken = default);
    Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default);
    Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default);
    Task RemoveManyAsync(IEnumerable<string> keys, CancellationToken cancellationToken = default);
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    Task<long> IncrementAsync(string key, long value = 1, CancellationToken cancellationToken = default);
    Task<long> DecrementAsync(string key, long value = 1, CancellationToken cancellationToken = default);
    Task<CacheStatistics> GetStatisticsAsync();
}

/// <summary>
/// Cache statistics
/// </summary>
public class CacheStatistics
{
    public long UsedMemoryBytes { get; set; }
    public long CacheHits { get; set; }
    public long CacheMisses { get; set; }
    public int ConnectedClients { get; set; }
    public double HitRatio => CacheHits + CacheMisses > 0 ? (double)CacheHits / (CacheHits + CacheMisses) : 0;
}
