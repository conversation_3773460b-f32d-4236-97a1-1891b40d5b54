namespace JimThread.Infrastructure.Messaging.Kafka;

/// <summary>
/// Configuration options for Kafka messaging
/// </summary>
public class KafkaOptions
{
    public const string SectionName = "Kafka";

    /// <summary>
    /// Kafka bootstrap servers (comma-separated list)
    /// </summary>
    public string BootstrapServers { get; set; } = "localhost:9092";

    /// <summary>
    /// Client ID for Kafka connections
    /// </summary>
    public string ClientId { get; set; } = "jimthread";

    /// <summary>
    /// Number of retries for failed operations
    /// </summary>
    public int Retries { get; set; } = 3;

    /// <summary>
    /// Retry backoff time in milliseconds
    /// </summary>
    public int RetryBackoffMs { get; set; } = 1000;

    /// <summary>
    /// Request timeout in milliseconds
    /// </summary>
    public int RequestTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Message timeout in milliseconds
    /// </summary>
    public int MessageTimeoutMs { get; set; } = 300000;

    /// <summary>
    /// Batch size for producer
    /// </summary>
    public int BatchSize { get; set; } = 16384;

    /// <summary>
    /// Linger time in milliseconds for batching
    /// </summary>
    public int LingerMs { get; set; } = 5;

    /// <summary>
    /// Consumer session timeout in milliseconds
    /// </summary>
    public int SessionTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Consumer heartbeat interval in milliseconds
    /// </summary>
    public int HeartbeatIntervalMs { get; set; } = 3000;

    /// <summary>
    /// Maximum poll interval in milliseconds
    /// </summary>
    public int MaxPollIntervalMs { get; set; } = 300000;

    /// <summary>
    /// Minimum bytes to fetch in a single request
    /// </summary>
    public int FetchMinBytes { get; set; } = 1;

    /// <summary>
    /// Maximum wait time for fetch requests in milliseconds
    /// </summary>
    public int FetchMaxWaitMs { get; set; } = 500;

    /// <summary>
    /// Topic configuration
    /// </summary>
    public KafkaTopics Topics { get; set; } = new();

    /// <summary>
    /// Security configuration
    /// </summary>
    public KafkaSecurity Security { get; set; } = new();
}

/// <summary>
/// Kafka topic names configuration
/// </summary>
public class KafkaTopics
{
    /// <summary>
    /// Topic for sensor data readings
    /// </summary>
    public string SensorData { get; set; } = "sensor-data";

    /// <summary>
    /// Topic for asset events
    /// </summary>
    public string AssetEvents { get; set; } = "asset-events";

    /// <summary>
    /// Topic for automation actions
    /// </summary>
    public string AutomationActions { get; set; } = "automation-actions";

    /// <summary>
    /// Topic for anomaly alerts
    /// </summary>
    public string AnomalyAlerts { get; set; } = "anomaly-alerts";

    /// <summary>
    /// Topic for system events
    /// </summary>
    public string SystemEvents { get; set; } = "system-events";

    /// <summary>
    /// Topic for audit logs
    /// </summary>
    public string AuditLogs { get; set; } = "audit-logs";

    /// <summary>
    /// Topic for connector status updates
    /// </summary>
    public string ConnectorStatus { get; set; } = "connector-status";

    /// <summary>
    /// Topic for model training events
    /// </summary>
    public string ModelTraining { get; set; } = "model-training";

    /// <summary>
    /// Topic for dead letter queue
    /// </summary>
    public string DeadLetterQueue { get; set; } = "dead-letter-queue";
}

/// <summary>
/// Kafka security configuration
/// </summary>
public class KafkaSecurity
{
    /// <summary>
    /// Security protocol (PLAINTEXT, SSL, SASL_PLAINTEXT, SASL_SSL)
    /// </summary>
    public string SecurityProtocol { get; set; } = "PLAINTEXT";

    /// <summary>
    /// SASL mechanism (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512, GSSAPI)
    /// </summary>
    public string? SaslMechanism { get; set; }

    /// <summary>
    /// SASL username
    /// </summary>
    public string? SaslUsername { get; set; }

    /// <summary>
    /// SASL password
    /// </summary>
    public string? SaslPassword { get; set; }

    /// <summary>
    /// SSL CA certificate location
    /// </summary>
    public string? SslCaLocation { get; set; }

    /// <summary>
    /// SSL certificate location
    /// </summary>
    public string? SslCertificateLocation { get; set; }

    /// <summary>
    /// SSL key location
    /// </summary>
    public string? SslKeyLocation { get; set; }

    /// <summary>
    /// SSL key password
    /// </summary>
    public string? SslKeyPassword { get; set; }

    /// <summary>
    /// Whether to enable SSL certificate verification
    /// </summary>
    public bool EnableSslCertificateVerification { get; set; } = true;
}

/// <summary>
/// Kafka topic configuration for creation
/// </summary>
public class KafkaTopicConfig
{
    /// <summary>
    /// Topic name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Number of partitions
    /// </summary>
    public int Partitions { get; set; } = 3;

    /// <summary>
    /// Replication factor
    /// </summary>
    public short ReplicationFactor { get; set; } = 1;

    /// <summary>
    /// Topic configuration properties
    /// </summary>
    public Dictionary<string, string> Config { get; set; } = new();

    /// <summary>
    /// Gets default topic configurations for JimThread
    /// </summary>
    /// <returns>List of topic configurations</returns>
    public static List<KafkaTopicConfig> GetDefaultTopics()
    {
        return new List<KafkaTopicConfig>
        {
            new()
            {
                Name = "sensor-data",
                Partitions = 6,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "604800000", // 7 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "asset-events",
                Partitions = 3,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "2592000000", // 30 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "automation-actions",
                Partitions = 3,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "2592000000", // 30 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "anomaly-alerts",
                Partitions = 3,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "7776000000", // 90 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "system-events",
                Partitions = 1,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "2592000000", // 30 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "audit-logs",
                Partitions = 1,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "31536000000", // 1 year
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "connector-status",
                Partitions = 1,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "604800000", // 7 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "model-training",
                Partitions = 1,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "2592000000", // 30 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            },
            new()
            {
                Name = "dead-letter-queue",
                Partitions = 1,
                ReplicationFactor = 1,
                Config = new Dictionary<string, string>
                {
                    ["retention.ms"] = "7776000000", // 90 days
                    ["compression.type"] = "snappy",
                    ["cleanup.policy"] = "delete"
                }
            }
        };
    }
}
