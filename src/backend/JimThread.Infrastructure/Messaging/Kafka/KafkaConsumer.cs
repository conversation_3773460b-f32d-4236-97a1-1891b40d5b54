using Confluent.Kafka;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using System.Text.Json;

namespace JimThread.Infrastructure.Messaging.Kafka;

/// <summary>
/// Base Kafka consumer for processing messages from topics
/// </summary>
public abstract class KafkaConsumerBase<T> : BackgroundService
{
    protected readonly IConsumer<string, string> _consumer;
    protected readonly ILogger _logger;
    protected readonly KafkaOptions _options;
    protected readonly string _topic;

    protected KafkaConsumerBase(
        IOptions<KafkaOptions> options, 
        ILogger logger,
        string topic,
        string consumerGroup)
    {
        _options = options.Value;
        _logger = logger;
        _topic = topic;

        var config = new ConsumerConfig
        {
            BootstrapServers = _options.BootstrapServers,
            GroupId = consumerGroup,
            ClientId = $"{_options.ClientId}-{consumerGroup}",
            AutoOffsetReset = AutoOffsetReset.Earliest,
            EnableAutoCommit = false,
            SessionTimeoutMs = _options.SessionTimeoutMs,
            HeartbeatIntervalMs = _options.HeartbeatIntervalMs,
            MaxPollIntervalMs = _options.MaxPollIntervalMs,
            FetchMinBytes = _options.FetchMinBytes,
            FetchMaxWaitMs = _options.FetchMaxWaitMs
        };

        _consumer = new ConsumerBuilder<string, string>(config)
            .SetErrorHandler((_, e) => _logger.LogError("Kafka consumer error: {Error}", e.Reason))
            .SetLogHandler((_, log) => _logger.LogDebug("Kafka consumer log: {Message}", log.Message))
            .Build();

        _logger.LogInformation("Kafka consumer initialized for topic {Topic} with group {GroupId}", 
            topic, consumerGroup);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _consumer.Subscribe(_topic);
        _logger.LogInformation("Subscribed to topic: {Topic}", _topic);

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var consumeResult = _consumer.Consume(stoppingToken);
                    
                    if (consumeResult?.Message != null)
                    {
                        await ProcessMessageAsync(consumeResult, stoppingToken);
                        _consumer.Commit(consumeResult);
                        
                        _logger.LogDebug("Processed message from topic {Topic} at offset {Offset}", 
                            _topic, consumeResult.Offset);
                    }
                }
                catch (ConsumeException ex)
                {
                    _logger.LogError(ex, "Error consuming message from topic {Topic}", _topic);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Consumer operation cancelled for topic {Topic}", _topic);
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error processing message from topic {Topic}", _topic);
                    
                    // Add delay to prevent tight loop on persistent errors
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
                }
            }
        }
        finally
        {
            _consumer.Close();
            _logger.LogInformation("Consumer closed for topic {Topic}", _topic);
        }
    }

    /// <summary>
    /// Processes a consumed message
    /// </summary>
    /// <param name="consumeResult">Consumed message result</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected abstract Task ProcessMessageAsync(ConsumeResult<string, string> consumeResult, CancellationToken cancellationToken);

    /// <summary>
    /// Deserializes a message value to the specified type
    /// </summary>
    /// <param name="messageValue">JSON message value</param>
    /// <returns>Deserialized object</returns>
    protected T? DeserializeMessage(string messageValue)
    {
        try
        {
            return JsonSerializer.Deserialize<T>(messageValue, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to deserialize message: {Message}", messageValue);
            return default;
        }
    }

    /// <summary>
    /// Gets header value as string
    /// </summary>
    /// <param name="headers">Message headers</param>
    /// <param name="key">Header key</param>
    /// <returns>Header value or null</returns>
    protected string? GetHeaderValue(Headers? headers, string key)
    {
        if (headers == null) return null;
        
        var header = headers.FirstOrDefault(h => h.Key == key);
        return header != null ? System.Text.Encoding.UTF8.GetString(header.GetValueBytes()) : null;
    }

    public override void Dispose()
    {
        _consumer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Consumer for sensor data messages
/// </summary>
public class SensorDataConsumer : KafkaConsumerBase<SensorDataMessage>
{
    private readonly ISensorDataProcessor _processor;

    public SensorDataConsumer(
        IOptions<KafkaOptions> options,
        ILogger<SensorDataConsumer> logger,
        ISensorDataProcessor processor)
        : base(options, logger, options.Value.Topics.SensorData, "sensor-data-processor")
    {
        _processor = processor;
    }

    protected override async Task ProcessMessageAsync(ConsumeResult<string, string> consumeResult, CancellationToken cancellationToken)
    {
        var sensorData = DeserializeMessage(consumeResult.Message.Value);
        if (sensorData != null)
        {
            await _processor.ProcessSensorDataAsync(sensorData, cancellationToken);
        }
    }
}

/// <summary>
/// Consumer for asset event messages
/// </summary>
public class AssetEventConsumer : KafkaConsumerBase<AssetEventMessage>
{
    private readonly IAssetEventProcessor _processor;

    public AssetEventConsumer(
        IOptions<KafkaOptions> options,
        ILogger<AssetEventConsumer> logger,
        IAssetEventProcessor processor)
        : base(options, logger, options.Value.Topics.AssetEvents, "asset-event-processor")
    {
        _processor = processor;
    }

    protected override async Task ProcessMessageAsync(ConsumeResult<string, string> consumeResult, CancellationToken cancellationToken)
    {
        var assetEvent = DeserializeMessage(consumeResult.Message.Value);
        if (assetEvent != null)
        {
            await _processor.ProcessAssetEventAsync(assetEvent, cancellationToken);
        }
    }
}

/// <summary>
/// Consumer for automation action messages
/// </summary>
public class AutomationActionConsumer : KafkaConsumerBase<AutomationActionMessage>
{
    private readonly IAutomationActionProcessor _processor;

    public AutomationActionConsumer(
        IOptions<KafkaOptions> options,
        ILogger<AutomationActionConsumer> logger,
        IAutomationActionProcessor processor)
        : base(options, logger, options.Value.Topics.AutomationActions, "automation-action-processor")
    {
        _processor = processor;
    }

    protected override async Task ProcessMessageAsync(ConsumeResult<string, string> consumeResult, CancellationToken cancellationToken)
    {
        var action = DeserializeMessage(consumeResult.Message.Value);
        if (action != null)
        {
            await _processor.ProcessAutomationActionAsync(action, cancellationToken);
        }
    }
}

/// <summary>
/// Consumer for anomaly alert messages
/// </summary>
public class AnomalyAlertConsumer : KafkaConsumerBase<AnomalyAlertMessage>
{
    private readonly IAnomalyAlertProcessor _processor;

    public AnomalyAlertConsumer(
        IOptions<KafkaOptions> options,
        ILogger<AnomalyAlertConsumer> logger,
        IAnomalyAlertProcessor processor)
        : base(options, logger, options.Value.Topics.AnomalyAlerts, "anomaly-alert-processor")
    {
        _processor = processor;
    }

    protected override async Task ProcessMessageAsync(ConsumeResult<string, string> consumeResult, CancellationToken cancellationToken)
    {
        var alert = DeserializeMessage(consumeResult.Message.Value);
        if (alert != null)
        {
            await _processor.ProcessAnomalyAlertAsync(alert, cancellationToken);
        }
    }
}

// Processor interfaces
public interface ISensorDataProcessor
{
    Task ProcessSensorDataAsync(SensorDataMessage sensorData, CancellationToken cancellationToken);
}

public interface IAssetEventProcessor
{
    Task ProcessAssetEventAsync(AssetEventMessage assetEvent, CancellationToken cancellationToken);
}

public interface IAutomationActionProcessor
{
    Task ProcessAutomationActionAsync(AutomationActionMessage action, CancellationToken cancellationToken);
}

public interface IAnomalyAlertProcessor
{
    Task ProcessAnomalyAlertAsync(AnomalyAlertMessage alert, CancellationToken cancellationToken);
}
