using Confluent.Kafka;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace JimThread.Infrastructure.Messaging.Kafka;

/// <summary>
/// Kafka message producer for publishing events to topics
/// </summary>
public class KafkaProducer : IDisposable
{
    private readonly IProducer<string, string> _producer;
    private readonly ILogger<KafkaProducer> _logger;
    private readonly KafkaOptions _options;

    public KafkaProducer(IOptions<KafkaOptions> options, ILogger<KafkaProducer> logger)
    {
        _options = options.Value;
        _logger = logger;

        var config = new ProducerConfig
        {
            BootstrapServers = _options.BootstrapServers,
            ClientId = _options.ClientId,
            Acks = Acks.All,
            Retries = _options.Retries,
            RetryBackoffMs = _options.RetryBackoffMs,
            RequestTimeoutMs = _options.RequestTimeoutMs,
            MessageTimeoutMs = _options.MessageTimeoutMs,
            CompressionType = CompressionType.Snappy,
            BatchSize = _options.BatchSize,
            LingerMs = _options.LingerMs,
            EnableIdempotence = true
        };

        _producer = new ProducerBuilder<string, string>(config)
            .SetErrorHandler((_, e) => _logger.LogError("Kafka producer error: {Error}", e.Reason))
            .SetLogHandler((_, log) => _logger.LogDebug("Kafka producer log: {Message}", log.Message))
            .Build();

        _logger.LogInformation("Kafka producer initialized with bootstrap servers: {BootstrapServers}", _options.BootstrapServers);
    }

    /// <summary>
    /// Publishes a message to a Kafka topic
    /// </summary>
    /// <typeparam name="T">Message type</typeparam>
    /// <param name="topic">Topic name</param>
    /// <param name="key">Message key</param>
    /// <param name="message">Message payload</param>
    /// <param name="headers">Optional headers</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery result</returns>
    public async Task<DeliveryResult<string, string>> PublishAsync<T>(
        string topic, 
        string key, 
        T message, 
        Dictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedMessage = JsonSerializer.Serialize(message, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var kafkaMessage = new Message<string, string>
            {
                Key = key,
                Value = serializedMessage,
                Timestamp = new Timestamp(DateTimeOffset.UtcNow)
            };

            if (headers != null)
            {
                kafkaMessage.Headers = new Headers();
                foreach (var header in headers)
                {
                    kafkaMessage.Headers.Add(header.Key, System.Text.Encoding.UTF8.GetBytes(header.Value));
                }
            }

            var result = await _producer.ProduceAsync(topic, kafkaMessage, cancellationToken);
            
            _logger.LogDebug("Message published to topic {Topic} at offset {Offset}", 
                topic, result.Offset);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish message to topic {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// Publishes a batch of messages to a Kafka topic
    /// </summary>
    /// <typeparam name="T">Message type</typeparam>
    /// <param name="topic">Topic name</param>
    /// <param name="messages">Messages to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of delivery results</returns>
    public async Task<IEnumerable<DeliveryResult<string, string>>> PublishBatchAsync<T>(
        string topic,
        IEnumerable<(string Key, T Message)> messages,
        CancellationToken cancellationToken = default)
    {
        var tasks = messages.Select(msg => PublishAsync(topic, msg.Key, msg.Message, cancellationToken: cancellationToken));
        return await Task.WhenAll(tasks);
    }

    /// <summary>
    /// Publishes a sensor data reading
    /// </summary>
    /// <param name="sensorReading">Sensor reading data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery result</returns>
    public async Task<DeliveryResult<string, string>> PublishSensorDataAsync(
        SensorDataMessage sensorReading,
        CancellationToken cancellationToken = default)
    {
        var headers = new Dictionary<string, string>
        {
            ["messageType"] = "SensorData",
            ["sensorId"] = sensorReading.SensorId.ToString(),
            ["assetId"] = sensorReading.AssetId.ToString(),
            ["timestamp"] = sensorReading.Timestamp.ToString("O")
        };

        return await PublishAsync(
            _options.Topics.SensorData,
            sensorReading.SensorTag,
            sensorReading,
            headers,
            cancellationToken);
    }

    /// <summary>
    /// Publishes an asset event
    /// </summary>
    /// <param name="assetEvent">Asset event data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery result</returns>
    public async Task<DeliveryResult<string, string>> PublishAssetEventAsync(
        AssetEventMessage assetEvent,
        CancellationToken cancellationToken = default)
    {
        var headers = new Dictionary<string, string>
        {
            ["messageType"] = "AssetEvent",
            ["assetId"] = assetEvent.AssetId.ToString(),
            ["eventType"] = assetEvent.EventType,
            ["timestamp"] = assetEvent.Timestamp.ToString("O")
        };

        return await PublishAsync(
            _options.Topics.AssetEvents,
            assetEvent.AssetId.ToString(),
            assetEvent,
            headers,
            cancellationToken);
    }

    /// <summary>
    /// Publishes an automation action
    /// </summary>
    /// <param name="action">Automation action</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery result</returns>
    public async Task<DeliveryResult<string, string>> PublishAutomationActionAsync(
        AutomationActionMessage action,
        CancellationToken cancellationToken = default)
    {
        var headers = new Dictionary<string, string>
        {
            ["messageType"] = "AutomationAction",
            ["actionType"] = action.ActionType,
            ["triggeredBy"] = action.TriggeredBy ?? "System",
            ["timestamp"] = action.Timestamp.ToString("O")
        };

        return await PublishAsync(
            _options.Topics.AutomationActions,
            action.ActionId.ToString(),
            action,
            headers,
            cancellationToken);
    }

    /// <summary>
    /// Publishes an anomaly alert
    /// </summary>
    /// <param name="alert">Anomaly alert</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Delivery result</returns>
    public async Task<DeliveryResult<string, string>> PublishAnomalyAlertAsync(
        AnomalyAlertMessage alert,
        CancellationToken cancellationToken = default)
    {
        var headers = new Dictionary<string, string>
        {
            ["messageType"] = "AnomalyAlert",
            ["sensorId"] = alert.SensorId.ToString(),
            ["assetId"] = alert.AssetId.ToString(),
            ["severity"] = alert.Severity,
            ["timestamp"] = alert.Timestamp.ToString("O")
        };

        return await PublishAsync(
            _options.Topics.AnomalyAlerts,
            alert.SensorId.ToString(),
            alert,
            headers,
            cancellationToken);
    }

    /// <summary>
    /// Flushes any pending messages
    /// </summary>
    /// <param name="timeout">Timeout for flush operation</param>
    public void Flush(TimeSpan timeout)
    {
        _producer.Flush(timeout);
    }

    public void Dispose()
    {
        try
        {
            _producer?.Flush(TimeSpan.FromSeconds(10));
            _producer?.Dispose();
            _logger.LogInformation("Kafka producer disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Kafka producer");
        }
    }
}

/// <summary>
/// Message representing sensor data reading
/// </summary>
public class SensorDataMessage
{
    public Guid SensorId { get; set; }
    public string SensorTag { get; set; } = string.Empty;
    public Guid AssetId { get; set; }
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Quality { get; set; } = "Good";
    public DateTimeOffset Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Message representing an asset event
/// </summary>
public class AssetEventMessage
{
    public Guid AssetId { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = "Info";
    public DateTimeOffset Timestamp { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public string? TriggeredBy { get; set; }
}

/// <summary>
/// Message representing an automation action
/// </summary>
public class AutomationActionMessage
{
    public Guid ActionId { get; set; }
    public string ActionType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid? TargetAssetId { get; set; }
    public Guid? TargetSensorId { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public DateTimeOffset Timestamp { get; set; }
    public string? TriggeredBy { get; set; }
    public string Status { get; set; } = "Pending";
}

/// <summary>
/// Message representing an anomaly alert
/// </summary>
public class AnomalyAlertMessage
{
    public Guid AlertId { get; set; }
    public Guid SensorId { get; set; }
    public string SensorTag { get; set; } = string.Empty;
    public Guid AssetId { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = "Medium";
    public double? Value { get; set; }
    public double? Threshold { get; set; }
    public double? Confidence { get; set; }
    public DateTimeOffset Timestamp { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();
}
