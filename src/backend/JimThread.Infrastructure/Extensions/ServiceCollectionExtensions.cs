using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using JimThread.Infrastructure.Data.Neo4j;
using JimThread.Infrastructure.Messaging.Kafka;
using JimThread.Infrastructure.Caching.Redis;
using JimThread.Core.Interfaces;
using JimThread.Infrastructure.Data.Repositories;
using StackExchange.Redis;
using Neo4j.Driver;

namespace JimThread.Infrastructure.Extensions;

/// <summary>
/// Extension methods for configuring infrastructure services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds JimThread infrastructure services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddJimThreadInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Add configuration options
        services.Configure<Neo4jOptions>(configuration.GetSection(Neo4jOptions.SectionName));
        services.Configure<KafkaOptions>(configuration.GetSection(KafkaOptions.SectionName));

        // Add Neo4j services
        services.AddNeo4j(configuration);

        // Add Kafka services
        services.AddKafka(configuration);

        // Add Redis services
        services.AddRedis(configuration);

        // Add repositories
        services.AddRepositories();

        // Add health checks
        services.AddInfrastructureHealthChecks(configuration);

        return services;
    }

    /// <summary>
    /// Adds Neo4j services
    /// </summary>
    private static IServiceCollection AddNeo4j(this IServiceCollection services, IConfiguration configuration)
    {
        var neo4jOptions = configuration.GetSection(Neo4jOptions.SectionName).Get<Neo4jOptions>() ?? new Neo4jOptions();

        // Register Neo4j driver
        services.AddSingleton<IDriver>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<IDriver>>();
            
            var config = ConfigBuilder.New()
                .WithEncryption(neo4jOptions.Encrypted)
                .WithTrustStrategy(TrustStrategy.TrustAllCertificates)
                .WithConnectionTimeout(TimeSpan.FromSeconds(neo4jOptions.ConnectionTimeoutSeconds))
                .WithMaxConnectionLifetime(TimeSpan.FromMinutes(neo4jOptions.MaxConnectionLifetimeMinutes))
                .WithMaxConnectionPoolSize(neo4jOptions.MaxConnectionPoolSize)
                .WithConnectionAcquisitionTimeout(TimeSpan.FromSeconds(neo4jOptions.ConnectionAcquisitionTimeoutSeconds))
                .Build();

            var driver = GraphDatabase.Driver(neo4jOptions.Uri, AuthTokens.Basic(neo4jOptions.Username, neo4jOptions.Password), config);
            
            logger.LogInformation("Neo4j driver registered for URI: {Uri}", neo4jOptions.Uri);
            return driver;
        });

        // Register Neo4j context
        services.AddScoped<Neo4jContext>();

        return services;
    }

    /// <summary>
    /// Adds Kafka services
    /// </summary>
    private static IServiceCollection AddKafka(this IServiceCollection services, IConfiguration configuration)
    {
        // Register Kafka producer
        services.AddSingleton<KafkaProducer>();

        // Register Kafka consumers as hosted services
        services.AddHostedService<SensorDataConsumer>();
        services.AddHostedService<AssetEventConsumer>();
        services.AddHostedService<AutomationActionConsumer>();
        services.AddHostedService<AnomalyAlertConsumer>();

        // Register message processors (these will be implemented in specific services)
        services.AddScoped<ISensorDataProcessor, DefaultSensorDataProcessor>();
        services.AddScoped<IAssetEventProcessor, DefaultAssetEventProcessor>();
        services.AddScoped<IAutomationActionProcessor, DefaultAutomationActionProcessor>();
        services.AddScoped<IAnomalyAlertProcessor, DefaultAnomalyAlertProcessor>();

        return services;
    }

    /// <summary>
    /// Adds Redis services
    /// </summary>
    private static IServiceCollection AddRedis(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";

        // Register Redis connection multiplexer
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<IConnectionMultiplexer>>();
            
            var options = ConfigurationOptions.Parse(connectionString);
            options.AbortOnConnectFail = false;
            options.ConnectRetry = 3;
            options.ConnectTimeout = 30000;
            options.SyncTimeout = 30000;

            var multiplexer = ConnectionMultiplexer.Connect(options);
            
            multiplexer.ConnectionFailed += (sender, args) =>
                logger.LogError("Redis connection failed: {EndPoint} - {FailureType}", args.EndPoint, args.FailureType);
            
            multiplexer.ConnectionRestored += (sender, args) =>
                logger.LogInformation("Redis connection restored: {EndPoint}", args.EndPoint);

            logger.LogInformation("Redis connection multiplexer registered for: {ConnectionString}", connectionString);
            return multiplexer;
        });

        // Register distributed cache
        services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = connectionString;
            options.InstanceName = "JimThread";
        });

        // Register cache service
        services.AddScoped<ICacheService, RedisCacheService>();

        return services;
    }

    /// <summary>
    /// Adds repository services
    /// </summary>
    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        services.AddScoped<IAssetRepository, Neo4jAssetRepository>();
        services.AddScoped<ISensorRepository, Neo4jSensorRepository>();

        return services;
    }

    /// <summary>
    /// Adds infrastructure health checks
    /// </summary>
    private static IServiceCollection AddInfrastructureHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        var healthChecksBuilder = services.AddHealthChecks();

        // Neo4j health check
        var neo4jUri = configuration.GetSection("Neo4j:Uri").Value ?? "bolt://localhost:7687";
        healthChecksBuilder.AddNeo4j(neo4jUri, name: "neo4j", tags: new[] { "database", "neo4j" });

        // Kafka health check
        var kafkaBootstrapServers = configuration.GetSection("Kafka:BootstrapServers").Value ?? "localhost:9092";
        healthChecksBuilder.AddKafka(options =>
        {
            options.BootstrapServers = kafkaBootstrapServers;
        }, name: "kafka", tags: new[] { "messaging", "kafka" });

        // Redis health check
        var redisConnectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
        healthChecksBuilder.AddRedis(redisConnectionString, name: "redis", tags: new[] { "cache", "redis" });

        return services;
    }
}

// Default message processor implementations (these would be moved to appropriate services later)
internal class DefaultSensorDataProcessor : ISensorDataProcessor
{
    private readonly ILogger<DefaultSensorDataProcessor> _logger;

    public DefaultSensorDataProcessor(ILogger<DefaultSensorDataProcessor> logger)
    {
        _logger = logger;
    }

    public Task ProcessSensorDataAsync(SensorDataMessage sensorData, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing sensor data for sensor {SensorTag}: {Value} {Unit}", 
            sensorData.SensorTag, sensorData.Value, sensorData.Unit);
        
        // TODO: Implement actual sensor data processing logic
        return Task.CompletedTask;
    }
}

internal class DefaultAssetEventProcessor : IAssetEventProcessor
{
    private readonly ILogger<DefaultAssetEventProcessor> _logger;

    public DefaultAssetEventProcessor(ILogger<DefaultAssetEventProcessor> logger)
    {
        _logger = logger;
    }

    public Task ProcessAssetEventAsync(AssetEventMessage assetEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing asset event for asset {AssetId}: {EventType} - {Description}", 
            assetEvent.AssetId, assetEvent.EventType, assetEvent.Description);
        
        // TODO: Implement actual asset event processing logic
        return Task.CompletedTask;
    }
}

internal class DefaultAutomationActionProcessor : IAutomationActionProcessor
{
    private readonly ILogger<DefaultAutomationActionProcessor> _logger;

    public DefaultAutomationActionProcessor(ILogger<DefaultAutomationActionProcessor> logger)
    {
        _logger = logger;
    }

    public Task ProcessAutomationActionAsync(AutomationActionMessage action, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing automation action {ActionId}: {ActionType} - {Description}", 
            action.ActionId, action.ActionType, action.Description);
        
        // TODO: Implement actual automation action processing logic
        return Task.CompletedTask;
    }
}

internal class DefaultAnomalyAlertProcessor : IAnomalyAlertProcessor
{
    private readonly ILogger<DefaultAnomalyAlertProcessor> _logger;

    public DefaultAnomalyAlertProcessor(ILogger<DefaultAnomalyAlertProcessor> logger)
    {
        _logger = logger;
    }

    public Task ProcessAnomalyAlertAsync(AnomalyAlertMessage alert, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing anomaly alert {AlertId} for sensor {SensorTag}: {AlertType} - {Severity}", 
            alert.AlertId, alert.SensorTag, alert.AlertType, alert.Severity);
        
        // TODO: Implement actual anomaly alert processing logic
        return Task.CompletedTask;
    }
}
