using JimThread.Core.Domain.Entities;

namespace JimThread.Core.Interfaces;

/// <summary>
/// Repository interface for Sensor entities with specialized operations
/// </summary>
public interface ISensorRepository : IRepository<Sensor>
{
    /// <summary>
    /// Gets sensors by asset ID
    /// </summary>
    /// <param name="assetId">Asset ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors for the specified asset</returns>
    Task<IEnumerable<Sensor>> GetByAssetIdAsync(Guid assetId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors by type
    /// </summary>
    /// <param name="sensorType">Sensor type to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors of the specified type</returns>
    Task<IEnumerable<Sensor>> GetByTypeAsync(string sensorType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors by status
    /// </summary>
    /// <param name="status">Sensor status to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors with the specified status</returns>
    Task<IEnumerable<Sensor>> GetByStatusAsync(SensorStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a sensor by its tag
    /// </summary>
    /// <param name="tag">Sensor tag</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Sensor with the specified tag or null if not found</returns>
    Task<Sensor?> GetByTagAsync(string tag, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors by multiple tags
    /// </summary>
    /// <param name="tags">Collection of sensor tags</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors with the specified tags</returns>
    Task<IEnumerable<Sensor>> GetByTagsAsync(IEnumerable<string> tags, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors that have anomaly detection enabled
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors with anomaly detection enabled</returns>
    Task<IEnumerable<Sensor>> GetAnomalyDetectionEnabledAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors that haven't reported data within the specified time period
    /// </summary>
    /// <param name="timeThreshold">Time threshold (sensors that haven't reported since this time)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of stale sensors</returns>
    Task<IEnumerable<Sensor>> GetStaleSensorsAsync(DateTimeOffset timeThreshold, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors with alarm configurations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors that have alarm configurations</returns>
    Task<IEnumerable<Sensor>> GetSensorsWithAlarmsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors by data source type
    /// </summary>
    /// <param name="sourceType">Data source type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors from the specified data source type</returns>
    Task<IEnumerable<Sensor>> GetByDataSourceTypeAsync(string sourceType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the last reading information for a sensor
    /// </summary>
    /// <param name="sensorId">Sensor ID</param>
    /// <param name="value">Last reading value</param>
    /// <param name="timestamp">Reading timestamp</param>
    /// <param name="quality">Data quality</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if updated successfully</returns>
    Task<bool> UpdateLastReadingAsync(Guid sensorId, double value, DateTimeOffset timestamp, DataQuality quality, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the status of multiple sensors
    /// </summary>
    /// <param name="sensorIds">Sensor IDs to update</param>
    /// <param name="status">New status</param>
    /// <param name="updatedBy">User performing the update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of sensors updated</returns>
    Task<int> UpdateStatusAsync(IEnumerable<Guid> sensorIds, SensorStatus status, string? updatedBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches sensors by tag, name, or description
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of matching sensors</returns>
    Task<IEnumerable<Sensor>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensor statistics grouped by type
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of sensor type to count</returns>
    Task<Dictionary<string, int>> GetSensorStatisticsByTypeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensor statistics grouped by status
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of sensor status to count</returns>
    Task<Dictionary<SensorStatus, int>> GetSensorStatisticsByStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors that are due for calibration
    /// </summary>
    /// <param name="daysAhead">Number of days ahead to look for upcoming calibrations</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors due for calibration</returns>
    Task<IEnumerable<Sensor>> GetSensorsDueForCalibrationAsync(int daysAhead = 30, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sensors with their associated assets
    /// </summary>
    /// <param name="sensorIds">Sensor IDs to include (optional, if null returns all)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of sensors with their assets</returns>
    Task<IEnumerable<Sensor>> GetWithAssetsAsync(IEnumerable<Guid>? sensorIds = null, CancellationToken cancellationToken = default);
}
