using JimThread.Core.Domain.Entities;

namespace JimThread.Core.Interfaces;

/// <summary>
/// Repository interface for Asset entities with specialized operations
/// </summary>
public interface IAssetRepository : IRepository<Asset>
{
    /// <summary>
    /// Gets assets by type
    /// </summary>
    /// <param name="assetType">Asset type to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of assets of the specified type</returns>
    Task<IEnumerable<Asset>> GetByTypeAsync(string assetType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets assets by status
    /// </summary>
    /// <param name="status">Asset status to filter by</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of assets with the specified status</returns>
    Task<IEnumerable<Asset>> GetByStatusAsync(AssetStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets child assets of a parent asset
    /// </summary>
    /// <param name="parentAssetId">Parent asset ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of child assets</returns>
    Task<IEnumerable<Asset>> GetChildAssetsAsync(Guid parentAssetId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the complete asset hierarchy starting from a root asset
    /// </summary>
    /// <param name="rootAssetId">Root asset ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Asset hierarchy tree</returns>
    Task<Asset?> GetAssetHierarchyAsync(Guid rootAssetId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all root assets (assets without a parent)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of root assets</returns>
    Task<IEnumerable<Asset>> GetRootAssetsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets assets by location
    /// </summary>
    /// <param name="building">Building name (optional)</param>
    /// <param name="floor">Floor (optional)</param>
    /// <param name="room">Room (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of assets at the specified location</returns>
    Task<IEnumerable<Asset>> GetByLocationAsync(string? building = null, string? floor = null, string? room = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets assets by criticality level
    /// </summary>
    /// <param name="criticalityLevel">Criticality level (1-5)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of assets with the specified criticality level</returns>
    Task<IEnumerable<Asset>> GetByCriticalityAsync(int criticalityLevel, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets assets that require maintenance (past due or due soon)
    /// </summary>
    /// <param name="daysAhead">Number of days ahead to look for upcoming maintenance</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of assets requiring maintenance</returns>
    Task<IEnumerable<Asset>> GetAssetsRequiringMaintenanceAsync(int daysAhead = 30, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches assets by name, description, or tags
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of matching assets</returns>
    Task<IEnumerable<Asset>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets assets with their sensors included
    /// </summary>
    /// <param name="assetIds">Asset IDs to include (optional, if null returns all)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of assets with sensors</returns>
    Task<IEnumerable<Asset>> GetWithSensorsAsync(IEnumerable<Guid>? assetIds = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the status of multiple assets
    /// </summary>
    /// <param name="assetIds">Asset IDs to update</param>
    /// <param name="status">New status</param>
    /// <param name="updatedBy">User performing the update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of assets updated</returns>
    Task<int> UpdateStatusAsync(IEnumerable<Guid> assetIds, AssetStatus status, string? updatedBy = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets asset statistics grouped by type
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of asset type to count</returns>
    Task<Dictionary<string, int>> GetAssetStatisticsByTypeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets asset statistics grouped by status
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of asset status to count</returns>
    Task<Dictionary<AssetStatus, int>> GetAssetStatisticsByStatusAsync(CancellationToken cancellationToken = default);
}
