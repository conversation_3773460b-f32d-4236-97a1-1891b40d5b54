using JimThread.Core.Domain.Entities;
using JimThread.Core.Domain.ValueObjects;

namespace JimThread.Core.Application.DTOs;

/// <summary>
/// Data Transfer Object for Asset entity
/// </summary>
public class AssetDto
{
    /// <summary>
    /// Unique identifier for the asset
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Human-readable name of the asset
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the asset
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Type/category of the asset
    /// </summary>
    public string AssetType { get; set; } = string.Empty;

    /// <summary>
    /// Manufacturer of the asset
    /// </summary>
    public string? Manufacturer { get; set; }

    /// <summary>
    /// Model number or identifier
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Serial number of the asset
    /// </summary>
    public string? SerialNumber { get; set; }

    /// <summary>
    /// Current operational status of the asset
    /// </summary>
    public AssetStatus Status { get; set; }

    /// <summary>
    /// Physical location of the asset
    /// </summary>
    public LocationDto? Location { get; set; }

    /// <summary>
    /// Parent asset ID (for hierarchical asset structures)
    /// </summary>
    public Guid? ParentAssetId { get; set; }

    /// <summary>
    /// Parent asset name
    /// </summary>
    public string? ParentAssetName { get; set; }

    /// <summary>
    /// Child assets
    /// </summary>
    public ICollection<AssetDto> ChildAssets { get; set; } = new List<AssetDto>();

    /// <summary>
    /// Sensors associated with this asset
    /// </summary>
    public ICollection<SensorDto> Sensors { get; set; } = new List<SensorDto>();

    /// <summary>
    /// Metadata and custom properties for the asset
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

    /// <summary>
    /// Tags for categorization and search
    /// </summary>
    public ICollection<string> Tags { get; set; } = new List<string>();

    /// <summary>
    /// Criticality level of the asset (1-5, where 5 is most critical)
    /// </summary>
    public int CriticalityLevel { get; set; }

    /// <summary>
    /// Installation date of the asset
    /// </summary>
    public DateTimeOffset? InstallationDate { get; set; }

    /// <summary>
    /// Last maintenance date
    /// </summary>
    public DateTimeOffset? LastMaintenanceDate { get; set; }

    /// <summary>
    /// Next scheduled maintenance date
    /// </summary>
    public DateTimeOffset? NextMaintenanceDate { get; set; }

    /// <summary>
    /// Timestamp when the asset was created
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }

    /// <summary>
    /// Timestamp when the asset was last updated
    /// </summary>
    public DateTimeOffset UpdatedAt { get; set; }

    /// <summary>
    /// User who created the asset
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// User who last updated the asset
    /// </summary>
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Data Transfer Object for Location value object
/// </summary>
public class LocationDto
{
    /// <summary>
    /// Building or facility name
    /// </summary>
    public string? Building { get; set; }

    /// <summary>
    /// Floor or level
    /// </summary>
    public string? Floor { get; set; }

    /// <summary>
    /// Room or area
    /// </summary>
    public string? Room { get; set; }

    /// <summary>
    /// Specific zone within the room/area
    /// </summary>
    public string? Zone { get; set; }

    /// <summary>
    /// GPS latitude coordinate
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// GPS longitude coordinate
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// Elevation in meters
    /// </summary>
    public double? Elevation { get; set; }

    /// <summary>
    /// Full address string
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Returns a string representation of the location
    /// </summary>
    public override string ToString()
    {
        var parts = new List<string>();

        if (!string.IsNullOrEmpty(Building))
            parts.Add($"Building: {Building}");

        if (!string.IsNullOrEmpty(Floor))
            parts.Add($"Floor: {Floor}");

        if (!string.IsNullOrEmpty(Room))
            parts.Add($"Room: {Room}");

        if (!string.IsNullOrEmpty(Zone))
            parts.Add($"Zone: {Zone}");

        if (Latitude.HasValue && Longitude.HasValue)
            parts.Add($"GPS: {Latitude:F6}, {Longitude:F6}");

        return parts.Count > 0 ? string.Join(", ", parts) : "Unknown Location";
    }
}
