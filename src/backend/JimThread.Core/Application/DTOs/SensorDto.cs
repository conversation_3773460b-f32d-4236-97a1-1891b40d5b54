using JimThread.Core.Domain.Entities;
using JimThread.Core.Domain.ValueObjects;

namespace JimThread.Core.Application.DTOs;

/// <summary>
/// Data Transfer Object for Sensor entity
/// </summary>
public class SensorDto
{
    /// <summary>
    /// Unique identifier for the sensor
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Unique tag identifier for the sensor
    /// </summary>
    public string Tag { get; set; } = string.Empty;

    /// <summary>
    /// Human-readable name of the sensor
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of what the sensor measures
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Type of sensor
    /// </summary>
    public string SensorType { get; set; } = string.Empty;

    /// <summary>
    /// Unit of measurement
    /// </summary>
    public string Unit { get; set; } = string.Empty;

    /// <summary>
    /// Data type of the sensor readings
    /// </summary>
    public SensorDataType DataType { get; set; }

    /// <summary>
    /// Minimum expected value for the sensor
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// Maximum expected value for the sensor
    /// </summary>
    public double? MaxValue { get; set; }

    /// <summary>
    /// Precision/number of decimal places for numeric values
    /// </summary>
    public int Precision { get; set; }

    /// <summary>
    /// Sampling rate in seconds
    /// </summary>
    public int SamplingRateSeconds { get; set; }

    /// <summary>
    /// Asset that this sensor is associated with
    /// </summary>
    public Guid AssetId { get; set; }

    /// <summary>
    /// Asset name
    /// </summary>
    public string? AssetName { get; set; }

    /// <summary>
    /// Current operational status of the sensor
    /// </summary>
    public SensorStatus Status { get; set; }

    /// <summary>
    /// Last known value from the sensor
    /// </summary>
    public double? LastValue { get; set; }

    /// <summary>
    /// Timestamp of the last reading
    /// </summary>
    public DateTimeOffset? LastReadingAt { get; set; }

    /// <summary>
    /// Quality indicator for the last reading
    /// </summary>
    public DataQuality LastReadingQuality { get; set; }

    /// <summary>
    /// Configuration for alarm thresholds
    /// </summary>
    public AlarmConfigurationDto? AlarmConfiguration { get; set; }

    /// <summary>
    /// Metadata and custom properties for the sensor
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

    /// <summary>
    /// Data source information
    /// </summary>
    public DataSourceDto? DataSource { get; set; }

    /// <summary>
    /// Whether this sensor is used for anomaly detection
    /// </summary>
    public bool EnableAnomalyDetection { get; set; }

    /// <summary>
    /// Whether this sensor data should be archived
    /// </summary>
    public bool EnableArchiving { get; set; }

    /// <summary>
    /// Data retention period in days
    /// </summary>
    public int RetentionDays { get; set; }

    /// <summary>
    /// Timestamp when the sensor was created
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }

    /// <summary>
    /// Timestamp when the sensor was last updated
    /// </summary>
    public DateTimeOffset UpdatedAt { get; set; }

    /// <summary>
    /// User who created the sensor
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// User who last updated the sensor
    /// </summary>
    public string? UpdatedBy { get; set; }
}

/// <summary>
/// Data Transfer Object for AlarmConfiguration value object
/// </summary>
public class AlarmConfigurationDto
{
    /// <summary>
    /// High-high alarm threshold (critical high)
    /// </summary>
    public double? HighHighThreshold { get; set; }

    /// <summary>
    /// High alarm threshold (warning high)
    /// </summary>
    public double? HighThreshold { get; set; }

    /// <summary>
    /// Low alarm threshold (warning low)
    /// </summary>
    public double? LowThreshold { get; set; }

    /// <summary>
    /// Low-low alarm threshold (critical low)
    /// </summary>
    public double? LowLowThreshold { get; set; }

    /// <summary>
    /// Deadband value to prevent alarm chattering
    /// </summary>
    public double Deadband { get; set; }

    /// <summary>
    /// Delay in seconds before triggering an alarm
    /// </summary>
    public int DelaySeconds { get; set; }

    /// <summary>
    /// Whether alarms are enabled for this sensor
    /// </summary>
    public bool AlarmsEnabled { get; set; }

    /// <summary>
    /// Priority level for alarms (1-5, where 5 is highest priority)
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// Whether to automatically acknowledge alarms when they return to normal
    /// </summary>
    public bool AutoAcknowledge { get; set; }

    /// <summary>
    /// Custom alarm message template
    /// </summary>
    public string? AlarmMessage { get; set; }
}

/// <summary>
/// Data Transfer Object for DataSource value object
/// </summary>
public class DataSourceDto
{
    /// <summary>
    /// Type of data source
    /// </summary>
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Connection string or endpoint for the data source
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Specific tag or point identifier in the source system
    /// </summary>
    public string SourceTag { get; set; } = string.Empty;

    /// <summary>
    /// Server or host name
    /// </summary>
    public string? ServerName { get; set; }

    /// <summary>
    /// Database or namespace within the source system
    /// </summary>
    public string? Database { get; set; }

    /// <summary>
    /// Additional configuration parameters as key-value pairs
    /// </summary>
    public Dictionary<string, string> Configuration { get; set; } = new Dictionary<string, string>();

    /// <summary>
    /// Whether the connection is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Last successful connection timestamp
    /// </summary>
    public DateTimeOffset? LastConnectionAt { get; set; }

    /// <summary>
    /// Last connection error message (if any)
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// Returns a string representation of the data source
    /// </summary>
    public override string ToString()
    {
        return $"{SourceType}: {SourceTag} @ {ServerName ?? ConnectionString}";
    }
}
