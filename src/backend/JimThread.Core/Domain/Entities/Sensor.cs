using System.ComponentModel.DataAnnotations;
using JimThread.Core.Domain.Common;
using JimThread.Core.Domain.ValueObjects;

namespace JimThread.Core.Domain.Entities;

/// <summary>
/// Represents a sensor that collects data from an asset
/// </summary>
public class Sensor : BaseEntity
{
    /// <summary>
    /// Unique tag identifier for the sensor (e.g., "TEMP_001", "PRESSURE_PUMP_A")
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Tag { get; set; } = string.Empty;

    /// <summary>
    /// Human-readable name of the sensor
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of what the sensor measures
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Type of sensor (e.g., "Temperature", "Pressure", "Flow", "Vibration")
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SensorType { get; set; } = string.Empty;

    /// <summary>
    /// Unit of measurement (e.g., "°C", "PSI", "GPM", "Hz")
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Unit { get; set; } = string.Empty;

    /// <summary>
    /// Data type of the sensor readings
    /// </summary>
    public SensorDataType DataType { get; set; } = SensorDataType.Numeric;

    /// <summary>
    /// Minimum expected value for the sensor
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// Maximum expected value for the sensor
    /// </summary>
    public double? MaxValue { get; set; }

    /// <summary>
    /// Precision/number of decimal places for numeric values
    /// </summary>
    public int Precision { get; set; } = 2;

    /// <summary>
    /// Sampling rate in seconds (how often the sensor is read)
    /// </summary>
    public int SamplingRateSeconds { get; set; } = 60;

    /// <summary>
    /// Asset that this sensor is associated with
    /// </summary>
    public Guid AssetId { get; set; }

    /// <summary>
    /// Asset navigation property
    /// </summary>
    public Asset Asset { get; set; } = null!;

    /// <summary>
    /// Current operational status of the sensor
    /// </summary>
    public SensorStatus Status { get; set; } = SensorStatus.Unknown;

    /// <summary>
    /// Last known value from the sensor
    /// </summary>
    public double? LastValue { get; set; }

    /// <summary>
    /// Timestamp of the last reading
    /// </summary>
    public DateTimeOffset? LastReadingAt { get; set; }

    /// <summary>
    /// Quality indicator for the last reading
    /// </summary>
    public DataQuality LastReadingQuality { get; set; } = DataQuality.Unknown;

    /// <summary>
    /// Configuration for alarm thresholds
    /// </summary>
    public AlarmConfiguration? AlarmConfiguration { get; set; }

    /// <summary>
    /// Metadata and custom properties for the sensor
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

    /// <summary>
    /// Data source information (historian, connector, etc.)
    /// </summary>
    public DataSource? DataSource { get; set; }

    /// <summary>
    /// Whether this sensor is used for anomaly detection
    /// </summary>
    public bool EnableAnomalyDetection { get; set; } = true;

    /// <summary>
    /// Whether this sensor data should be archived
    /// </summary>
    public bool EnableArchiving { get; set; } = true;

    /// <summary>
    /// Data retention period in days
    /// </summary>
    public int RetentionDays { get; set; } = 365;
}

/// <summary>
/// Data type of sensor readings
/// </summary>
public enum SensorDataType
{
    Numeric = 0,
    Boolean = 1,
    Text = 2,
    Enum = 3
}

/// <summary>
/// Operational status of a sensor
/// </summary>
public enum SensorStatus
{
    Unknown = 0,
    Online = 1,
    Offline = 2,
    Error = 3,
    Maintenance = 4,
    Calibration = 5
}

/// <summary>
/// Quality indicator for sensor data
/// </summary>
public enum DataQuality
{
    Unknown = 0,
    Good = 1,
    Bad = 2,
    Uncertain = 3,
    Stale = 4
}
