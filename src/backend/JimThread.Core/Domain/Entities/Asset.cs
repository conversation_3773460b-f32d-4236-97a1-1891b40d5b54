using System.ComponentModel.DataAnnotations;
using JimThread.Core.Domain.Common;
using JimThread.Core.Domain.ValueObjects;

namespace JimThread.Core.Domain.Entities;

/// <summary>
/// Represents a physical or logical asset in the industrial environment
/// </summary>
public class Asset : BaseEntity
{
    /// <summary>
    /// Human-readable name of the asset
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the asset
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Type/category of the asset (e.g., "Motor", "Pump", "Conveyor")
    /// </summary>
    [Required]
    [StringLength(100)]
    public string AssetType { get; set; } = string.Empty;

    /// <summary>
    /// Manufacturer of the asset
    /// </summary>
    [StringLength(100)]
    public string? Manufacturer { get; set; }

    /// <summary>
    /// Model number or identifier
    /// </summary>
    [StringLength(100)]
    public string? Model { get; set; }

    /// <summary>
    /// Serial number of the asset
    /// </summary>
    [StringLength(100)]
    public string? SerialNumber { get; set; }

    /// <summary>
    /// Current operational status of the asset
    /// </summary>
    public AssetStatus Status { get; set; } = AssetStatus.Unknown;

    /// <summary>
    /// Physical location of the asset
    /// </summary>
    public Location? Location { get; set; }

    /// <summary>
    /// Parent asset ID (for hierarchical asset structures)
    /// </summary>
    public Guid? ParentAssetId { get; set; }

    /// <summary>
    /// Parent asset navigation property
    /// </summary>
    public Asset? ParentAsset { get; set; }

    /// <summary>
    /// Child assets
    /// </summary>
    public ICollection<Asset> ChildAssets { get; set; } = new List<Asset>();

    /// <summary>
    /// Sensors associated with this asset
    /// </summary>
    public ICollection<Sensor> Sensors { get; set; } = new List<Sensor>();

    /// <summary>
    /// Metadata and custom properties for the asset
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

    /// <summary>
    /// Tags for categorization and search
    /// </summary>
    public ICollection<string> Tags { get; set; } = new List<string>();

    /// <summary>
    /// Criticality level of the asset (1-5, where 5 is most critical)
    /// </summary>
    [Range(1, 5)]
    public int CriticalityLevel { get; set; } = 3;

    /// <summary>
    /// Installation date of the asset
    /// </summary>
    public DateTimeOffset? InstallationDate { get; set; }

    /// <summary>
    /// Last maintenance date
    /// </summary>
    public DateTimeOffset? LastMaintenanceDate { get; set; }

    /// <summary>
    /// Next scheduled maintenance date
    /// </summary>
    public DateTimeOffset? NextMaintenanceDate { get; set; }
}

/// <summary>
/// Operational status of an asset
/// </summary>
public enum AssetStatus
{
    Unknown = 0,
    Online = 1,
    Offline = 2,
    Maintenance = 3,
    Error = 4,
    Warning = 5,
    Decommissioned = 6
}
