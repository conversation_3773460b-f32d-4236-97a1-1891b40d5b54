using System.ComponentModel.DataAnnotations;

namespace JimThread.Core.Domain.ValueObjects;

/// <summary>
/// Value object representing a physical location
/// </summary>
public class Location : IEquatable<Location>
{
    /// <summary>
    /// Building or facility name
    /// </summary>
    [StringLength(100)]
    public string? Building { get; set; }

    /// <summary>
    /// Floor or level
    /// </summary>
    [StringLength(50)]
    public string? Floor { get; set; }

    /// <summary>
    /// Room or area
    /// </summary>
    [StringLength(100)]
    public string? Room { get; set; }

    /// <summary>
    /// Specific zone within the room/area
    /// </summary>
    [StringLength(50)]
    public string? Zone { get; set; }

    /// <summary>
    /// GPS latitude coordinate
    /// </summary>
    [Range(-90.0, 90.0)]
    public double? Latitude { get; set; }

    /// <summary>
    /// GPS longitude coordinate
    /// </summary>
    [Range(-180.0, 180.0)]
    public double? Longitude { get; set; }

    /// <summary>
    /// Elevation in meters
    /// </summary>
    public double? Elevation { get; set; }

    /// <summary>
    /// Full address string
    /// </summary>
    [StringLength(500)]
    public string? Address { get; set; }

    /// <summary>
    /// Creates a new Location instance
    /// </summary>
    public Location() { }

    /// <summary>
    /// Creates a new Location instance with basic information
    /// </summary>
    /// <param name="building">Building name</param>
    /// <param name="floor">Floor</param>
    /// <param name="room">Room</param>
    public Location(string? building, string? floor, string? room)
    {
        Building = building;
        Floor = floor;
        Room = room;
    }

    /// <summary>
    /// Creates a new Location instance with GPS coordinates
    /// </summary>
    /// <param name="latitude">Latitude</param>
    /// <param name="longitude">Longitude</param>
    /// <param name="elevation">Elevation</param>
    public Location(double latitude, double longitude, double? elevation = null)
    {
        Latitude = latitude;
        Longitude = longitude;
        Elevation = elevation;
    }

    /// <summary>
    /// Returns a string representation of the location
    /// </summary>
    public override string ToString()
    {
        var parts = new List<string>();

        if (!string.IsNullOrEmpty(Building))
            parts.Add($"Building: {Building}");

        if (!string.IsNullOrEmpty(Floor))
            parts.Add($"Floor: {Floor}");

        if (!string.IsNullOrEmpty(Room))
            parts.Add($"Room: {Room}");

        if (!string.IsNullOrEmpty(Zone))
            parts.Add($"Zone: {Zone}");

        if (Latitude.HasValue && Longitude.HasValue)
            parts.Add($"GPS: {Latitude:F6}, {Longitude:F6}");

        return parts.Count > 0 ? string.Join(", ", parts) : "Unknown Location";
    }

    /// <summary>
    /// Checks equality with another Location
    /// </summary>
    public bool Equals(Location? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;

        return Building == other.Building &&
               Floor == other.Floor &&
               Room == other.Room &&
               Zone == other.Zone &&
               Math.Abs((Latitude ?? 0) - (other.Latitude ?? 0)) < 0.000001 &&
               Math.Abs((Longitude ?? 0) - (other.Longitude ?? 0)) < 0.000001 &&
               Math.Abs((Elevation ?? 0) - (other.Elevation ?? 0)) < 0.01;
    }

    /// <summary>
    /// Checks equality with another object
    /// </summary>
    public override bool Equals(object? obj)
    {
        return Equals(obj as Location);
    }

    /// <summary>
    /// Gets the hash code for this location
    /// </summary>
    public override int GetHashCode()
    {
        return HashCode.Combine(Building, Floor, Room, Zone, Latitude, Longitude, Elevation);
    }

    /// <summary>
    /// Equality operator
    /// </summary>
    public static bool operator ==(Location? left, Location? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Inequality operator
    /// </summary>
    public static bool operator !=(Location? left, Location? right)
    {
        return !Equals(left, right);
    }
}
