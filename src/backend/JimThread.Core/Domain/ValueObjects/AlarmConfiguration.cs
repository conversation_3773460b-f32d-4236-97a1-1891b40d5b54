using System.ComponentModel.DataAnnotations;

namespace JimThread.Core.Domain.ValueObjects;

/// <summary>
/// Configuration for sensor alarm thresholds and behavior
/// </summary>
public class AlarmConfiguration : IEquatable<AlarmConfiguration>
{
    /// <summary>
    /// High-high alarm threshold (critical high)
    /// </summary>
    public double? HighHighThreshold { get; set; }

    /// <summary>
    /// High alarm threshold (warning high)
    /// </summary>
    public double? HighThreshold { get; set; }

    /// <summary>
    /// Low alarm threshold (warning low)
    /// </summary>
    public double? LowThreshold { get; set; }

    /// <summary>
    /// Low-low alarm threshold (critical low)
    /// </summary>
    public double? LowLowThreshold { get; set; }

    /// <summary>
    /// Deadband value to prevent alarm chattering
    /// </summary>
    [Range(0, double.MaxValue)]
    public double Deadband { get; set; } = 0.0;

    /// <summary>
    /// Delay in seconds before triggering an alarm
    /// </summary>
    [Range(0, int.MaxValue)]
    public int DelaySeconds { get; set; } = 0;

    /// <summary>
    /// Whether alarms are enabled for this sensor
    /// </summary>
    public bool AlarmsEnabled { get; set; } = true;

    /// <summary>
    /// Priority level for alarms (1-5, where 5 is highest priority)
    /// </summary>
    [Range(1, 5)]
    public int Priority { get; set; } = 3;

    /// <summary>
    /// Whether to automatically acknowledge alarms when they return to normal
    /// </summary>
    public bool AutoAcknowledge { get; set; } = false;

    /// <summary>
    /// Custom alarm message template
    /// </summary>
    [StringLength(500)]
    public string? AlarmMessage { get; set; }

    /// <summary>
    /// Creates a new AlarmConfiguration instance
    /// </summary>
    public AlarmConfiguration() { }

    /// <summary>
    /// Creates a new AlarmConfiguration with basic thresholds
    /// </summary>
    /// <param name="lowThreshold">Low warning threshold</param>
    /// <param name="highThreshold">High warning threshold</param>
    /// <param name="lowLowThreshold">Low critical threshold</param>
    /// <param name="highHighThreshold">High critical threshold</param>
    public AlarmConfiguration(double? lowThreshold, double? highThreshold, 
                            double? lowLowThreshold = null, double? highHighThreshold = null)
    {
        LowThreshold = lowThreshold;
        HighThreshold = highThreshold;
        LowLowThreshold = lowLowThreshold;
        HighHighThreshold = highHighThreshold;
    }

    /// <summary>
    /// Validates the alarm configuration
    /// </summary>
    /// <returns>True if configuration is valid</returns>
    public bool IsValid()
    {
        // Check that thresholds are in logical order
        if (LowLowThreshold.HasValue && LowThreshold.HasValue && LowLowThreshold >= LowThreshold)
            return false;

        if (LowThreshold.HasValue && HighThreshold.HasValue && LowThreshold >= HighThreshold)
            return false;

        if (HighThreshold.HasValue && HighHighThreshold.HasValue && HighThreshold >= HighHighThreshold)
            return false;

        return true;
    }

    /// <summary>
    /// Determines the alarm state for a given value
    /// </summary>
    /// <param name="value">Sensor value to check</param>
    /// <returns>Alarm state</returns>
    public AlarmState GetAlarmState(double value)
    {
        if (!AlarmsEnabled)
            return AlarmState.Normal;

        if (HighHighThreshold.HasValue && value >= HighHighThreshold.Value)
            return AlarmState.HighHigh;

        if (LowLowThreshold.HasValue && value <= LowLowThreshold.Value)
            return AlarmState.LowLow;

        if (HighThreshold.HasValue && value >= HighThreshold.Value)
            return AlarmState.High;

        if (LowThreshold.HasValue && value <= LowThreshold.Value)
            return AlarmState.Low;

        return AlarmState.Normal;
    }

    /// <summary>
    /// Checks equality with another AlarmConfiguration
    /// </summary>
    public bool Equals(AlarmConfiguration? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;

        return HighHighThreshold == other.HighHighThreshold &&
               HighThreshold == other.HighThreshold &&
               LowThreshold == other.LowThreshold &&
               LowLowThreshold == other.LowLowThreshold &&
               Math.Abs(Deadband - other.Deadband) < 0.000001 &&
               DelaySeconds == other.DelaySeconds &&
               AlarmsEnabled == other.AlarmsEnabled &&
               Priority == other.Priority &&
               AutoAcknowledge == other.AutoAcknowledge &&
               AlarmMessage == other.AlarmMessage;
    }

    /// <summary>
    /// Checks equality with another object
    /// </summary>
    public override bool Equals(object? obj)
    {
        return Equals(obj as AlarmConfiguration);
    }

    /// <summary>
    /// Gets the hash code for this configuration
    /// </summary>
    public override int GetHashCode()
    {
        return HashCode.Combine(HighHighThreshold, HighThreshold, LowThreshold, LowLowThreshold,
                               Deadband, DelaySeconds, AlarmsEnabled, Priority);
    }

    /// <summary>
    /// Equality operator
    /// </summary>
    public static bool operator ==(AlarmConfiguration? left, AlarmConfiguration? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Inequality operator
    /// </summary>
    public static bool operator !=(AlarmConfiguration? left, AlarmConfiguration? right)
    {
        return !Equals(left, right);
    }
}

/// <summary>
/// Alarm state enumeration
/// </summary>
public enum AlarmState
{
    Normal = 0,
    Low = 1,
    High = 2,
    LowLow = 3,
    HighHigh = 4
}
