using System.ComponentModel.DataAnnotations;

namespace JimThread.Core.Domain.ValueObjects;

/// <summary>
/// Information about the data source for a sensor
/// </summary>
public class DataSource : IEquatable<DataSource>
{
    /// <summary>
    /// Type of data source (e.g., "OSIsoft PI", "Ignition", "MQTT", "OPC UA")
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SourceType { get; set; } = string.Empty;

    /// <summary>
    /// Connection string or endpoint for the data source
    /// </summary>
    [Required]
    [StringLength(500)]
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Specific tag or point identifier in the source system
    /// </summary>
    [Required]
    [StringLength(200)]
    public string SourceTag { get; set; } = string.Empty;

    /// <summary>
    /// Server or host name
    /// </summary>
    [StringLength(100)]
    public string? ServerName { get; set; }

    /// <summary>
    /// Database or namespace within the source system
    /// </summary>
    [StringLength(100)]
    public string? Database { get; set; }

    /// <summary>
    /// Additional configuration parameters as key-value pairs
    /// </summary>
    public Dictionary<string, string> Configuration { get; set; } = new Dictionary<string, string>();

    /// <summary>
    /// Authentication information (if required)
    /// </summary>
    public AuthenticationInfo? Authentication { get; set; }

    /// <summary>
    /// Whether the connection is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Last successful connection timestamp
    /// </summary>
    public DateTimeOffset? LastConnectionAt { get; set; }

    /// <summary>
    /// Last connection error message (if any)
    /// </summary>
    [StringLength(1000)]
    public string? LastError { get; set; }

    /// <summary>
    /// Creates a new DataSource instance
    /// </summary>
    public DataSource() { }

    /// <summary>
    /// Creates a new DataSource instance with basic information
    /// </summary>
    /// <param name="sourceType">Type of data source</param>
    /// <param name="connectionString">Connection string</param>
    /// <param name="sourceTag">Source tag identifier</param>
    public DataSource(string sourceType, string connectionString, string sourceTag)
    {
        SourceType = sourceType;
        ConnectionString = connectionString;
        SourceTag = sourceTag;
    }

    /// <summary>
    /// Gets a configuration value by key
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <returns>Configuration value or null if not found</returns>
    public string? GetConfigurationValue(string key)
    {
        return Configuration.TryGetValue(key, out var value) ? value : null;
    }

    /// <summary>
    /// Sets a configuration value
    /// </summary>
    /// <param name="key">Configuration key</param>
    /// <param name="value">Configuration value</param>
    public void SetConfigurationValue(string key, string value)
    {
        Configuration[key] = value;
    }

    /// <summary>
    /// Validates the data source configuration
    /// </summary>
    /// <returns>True if configuration is valid</returns>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(SourceType) &&
               !string.IsNullOrEmpty(ConnectionString) &&
               !string.IsNullOrEmpty(SourceTag);
    }

    /// <summary>
    /// Returns a string representation of the data source
    /// </summary>
    public override string ToString()
    {
        return $"{SourceType}: {SourceTag} @ {ServerName ?? ConnectionString}";
    }

    /// <summary>
    /// Checks equality with another DataSource
    /// </summary>
    public bool Equals(DataSource? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;

        return SourceType == other.SourceType &&
               ConnectionString == other.ConnectionString &&
               SourceTag == other.SourceTag &&
               ServerName == other.ServerName &&
               Database == other.Database;
    }

    /// <summary>
    /// Checks equality with another object
    /// </summary>
    public override bool Equals(object? obj)
    {
        return Equals(obj as DataSource);
    }

    /// <summary>
    /// Gets the hash code for this data source
    /// </summary>
    public override int GetHashCode()
    {
        return HashCode.Combine(SourceType, ConnectionString, SourceTag, ServerName, Database);
    }

    /// <summary>
    /// Equality operator
    /// </summary>
    public static bool operator ==(DataSource? left, DataSource? right)
    {
        return Equals(left, right);
    }

    /// <summary>
    /// Inequality operator
    /// </summary>
    public static bool operator !=(DataSource? left, DataSource? right)
    {
        return !Equals(left, right);
    }
}

/// <summary>
/// Authentication information for data sources
/// </summary>
public class AuthenticationInfo
{
    /// <summary>
    /// Authentication type (e.g., "Basic", "Windows", "Certificate", "Token")
    /// </summary>
    [Required]
    [StringLength(50)]
    public string AuthType { get; set; } = string.Empty;

    /// <summary>
    /// Username (if applicable)
    /// </summary>
    [StringLength(100)]
    public string? Username { get; set; }

    /// <summary>
    /// Encrypted password or token
    /// </summary>
    [StringLength(500)]
    public string? EncryptedCredential { get; set; }

    /// <summary>
    /// Certificate thumbprint (if using certificate authentication)
    /// </summary>
    [StringLength(100)]
    public string? CertificateThumbprint { get; set; }

    /// <summary>
    /// Additional authentication parameters
    /// </summary>
    public Dictionary<string, string> Parameters { get; set; } = new Dictionary<string, string>();
}
