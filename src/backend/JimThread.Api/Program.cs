using JimThread.Api.Extensions;
using JimThread.Infrastructure.Extensions;
using Serilog;
using Prometheus;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/jimthread-api-.txt", rollingInterval: RollingInterval.Day)
    .Enrich.FromLogContext()
    .Enrich.WithEnvironmentName()
    .Enrich.WithMachineName()
    .Enrich.WithProcessId()
    .Enrich.WithThreadId()
    .CreateLogger();

try
{
    Log.Information("Starting JimThread API Gateway");

    var builder = WebApplication.CreateBuilder(args);

    // Use Serilog
    builder.Host.UseSerilog();

    // Add services to the container
    builder.Services.AddControllers();
    builder.Services.AddEndpointsApiExplorer();

    // Add custom services
    builder.Services.AddJimThreadApi(builder.Configuration);
    builder.Services.AddJimThreadInfrastructure(builder.Configuration);

    // Add OpenTelemetry
    builder.Services.AddOpenTelemetry()
        .WithMetrics(builder =>
        {
            builder
                .SetResourceBuilder(ResourceBuilder.CreateDefault()
                    .AddService("JimThread.Api", "1.0.0"))
                .AddAspNetCoreInstrumentation()
                .AddHttpClientInstrumentation()
                .AddPrometheusExporter();
        });

    var app = builder.Build();

    // Configure the HTTP request pipeline
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "JimThread API v1");
            c.RoutePrefix = string.Empty; // Serve Swagger UI at root
        });
    }

    // Security headers
    app.UseSecurityHeaders();

    // CORS
    app.UseCors("JimThreadCorsPolicy");

    // Authentication & Authorization
    app.UseAuthentication();
    app.UseAuthorization();

    // Prometheus metrics
    app.UseMetricServer();
    app.UseHttpMetrics();

    // Health checks
    app.UseHealthChecks();

    // Request logging
    app.UseSerilogRequestLogging();

    // Controllers
    app.MapControllers();

    // Default route
    app.MapGet("/", () => new
    {
        Service = "JimThread API Gateway",
        Version = "1.0.0",
        Environment = app.Environment.EnvironmentName,
        Timestamp = DateTimeOffset.UtcNow,
        Status = "Healthy"
    });

    Log.Information("JimThread API Gateway started successfully");
    
    await app.RunAsync();
}
catch (Exception ex)
{
    Log.Fatal(ex, "JimThread API Gateway terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
