{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"Neo4j": "bolt://localhost:7687", "Kafka": "localhost:9092", "Redis": "localhost:6379"}, "Neo4j": {"Uri": "bolt://localhost:7687", "Username": "neo4j", "Password": "devpassword", "Encrypted": false, "ConnectionTimeoutSeconds": 30, "MaxConnectionLifetimeMinutes": 60, "MaxConnectionPoolSize": 100, "ConnectionAcquisitionTimeoutSeconds": 60, "Database": null}, "Kafka": {"BootstrapServers": "localhost:9092", "ClientId": "jimthread-api", "Retries": 3, "RetryBackoffMs": 1000, "RequestTimeoutMs": 30000, "MessageTimeoutMs": 300000, "BatchSize": 16384, "LingerMs": 5, "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "MaxPollIntervalMs": 300000, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "Topics": {"SensorData": "sensor-data", "AssetEvents": "asset-events", "AutomationActions": "automation-actions", "AnomalyAlerts": "anomaly-alerts", "SystemEvents": "system-events", "AuditLogs": "audit-logs", "ConnectorStatus": "connector-status", "ModelTraining": "model-training", "DeadLetterQueue": "dead-letter-queue"}, "Security": {"SecurityProtocol": "PLAINTEXT", "EnableSslCertificateVerification": true}}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-for-development-only-change-in-production", "Issuer": "<PERSON><PERSON><PERSON><PERSON>", "Audience": "JimThread.Api", "ExpirationMinutes": 60}, "Cors": {"Origins": ["http://localhost:3000", "https://localhost:3000", "http://localhost:3001", "https://localhost:3001"]}, "Features": {"EnableSwagger": true, "EnableDetailedErrors": true, "EnableCors": true, "EnableMetrics": true, "EnableHealthChecks": true}, "Cache": {"DefaultExpirationMinutes": 15, "SensorDataExpirationMinutes": 5, "AssetDataExpirationMinutes": 30, "UserSessionExpirationMinutes": 60}, "RateLimiting": {"EnableRateLimiting": true, "RequestsPerMinute": 100, "BurstSize": 20}, "Monitoring": {"ApplicationName": "JimThread.Api", "Version": "1.0.0", "Environment": "Development"}}