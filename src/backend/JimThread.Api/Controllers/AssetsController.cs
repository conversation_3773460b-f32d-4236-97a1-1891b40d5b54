using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using JimThread.Core.Interfaces;
using JimThread.Core.Application.DTOs;
using JimThread.Core.Domain.Entities;
using AutoMapper;

namespace JimThread.Api.Controllers;

/// <summary>
/// API controller for managing assets
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class AssetsController : ControllerBase
{
    private readonly IAssetRepository _assetRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<AssetsController> _logger;

    public AssetsController(
        IAssetRepository assetRepository,
        IMapper mapper,
        ILogger<AssetsController> logger)
    {
        _assetRepository = assetRepository;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    /// Gets all assets
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of assets</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<AssetDto>), 200)]
    public async Task<ActionResult<IEnumerable<AssetDto>>> GetAssets(CancellationToken cancellationToken)
    {
        try
        {
            var assets = await _assetRepository.GetAllAsync(cancellationToken);
            var assetDtos = _mapper.Map<IEnumerable<AssetDto>>(assets);
            
            _logger.LogInformation("Retrieved {Count} assets", assetDtos.Count());
            return Ok(assetDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving assets");
            return StatusCode(500, "An error occurred while retrieving assets");
        }
    }

    /// <summary>
    /// Gets a specific asset by ID
    /// </summary>
    /// <param name="id">Asset ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Asset details</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(AssetDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AssetDto>> GetAsset(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var asset = await _assetRepository.GetByIdAsync(id, cancellationToken);
            
            if (asset == null)
            {
                _logger.LogWarning("Asset with ID {AssetId} not found", id);
                return NotFound($"Asset with ID {id} not found");
            }

            var assetDto = _mapper.Map<AssetDto>(asset);
            _logger.LogInformation("Retrieved asset {AssetId}: {AssetName}", id, asset.Name);
            
            return Ok(assetDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving asset {AssetId}", id);
            return StatusCode(500, "An error occurred while retrieving the asset");
        }
    }

    /// <summary>
    /// Creates a new asset
    /// </summary>
    /// <param name="createAssetDto">Asset creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created asset</returns>
    [HttpPost]
    [ProducesResponseType(typeof(AssetDto), 201)]
    [ProducesResponseType(400)]
    [Authorize(Policy = "RequireEngineerRole")]
    public async Task<ActionResult<AssetDto>> CreateAsset(CreateAssetDto createAssetDto, CancellationToken cancellationToken)
    {
        try
        {
            var asset = _mapper.Map<Asset>(createAssetDto);
            asset.CreatedBy = User.Identity?.Name;
            
            var createdAsset = await _assetRepository.AddAsync(asset, cancellationToken);
            var assetDto = _mapper.Map<AssetDto>(createdAsset);
            
            _logger.LogInformation("Created asset {AssetId}: {AssetName}", createdAsset.Id, createdAsset.Name);
            
            return CreatedAtAction(nameof(GetAsset), new { id = createdAsset.Id }, assetDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating asset");
            return StatusCode(500, "An error occurred while creating the asset");
        }
    }

    /// <summary>
    /// Updates an existing asset
    /// </summary>
    /// <param name="id">Asset ID</param>
    /// <param name="updateAssetDto">Asset update data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated asset</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(AssetDto), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(400)]
    [Authorize(Policy = "RequireEngineerRole")]
    public async Task<ActionResult<AssetDto>> UpdateAsset(Guid id, UpdateAssetDto updateAssetDto, CancellationToken cancellationToken)
    {
        try
        {
            var existingAsset = await _assetRepository.GetByIdAsync(id, cancellationToken);
            
            if (existingAsset == null)
            {
                _logger.LogWarning("Asset with ID {AssetId} not found for update", id);
                return NotFound($"Asset with ID {id} not found");
            }

            _mapper.Map(updateAssetDto, existingAsset);
            existingAsset.UpdatedBy = User.Identity?.Name;
            
            var updatedAsset = await _assetRepository.UpdateAsync(existingAsset, cancellationToken);
            var assetDto = _mapper.Map<AssetDto>(updatedAsset);
            
            _logger.LogInformation("Updated asset {AssetId}: {AssetName}", id, updatedAsset.Name);
            
            return Ok(assetDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating asset {AssetId}", id);
            return StatusCode(500, "An error occurred while updating the asset");
        }
    }

    /// <summary>
    /// Deletes an asset (soft delete)
    /// </summary>
    /// <param name="id">Asset ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [Authorize(Policy = "RequireAdministratorRole")]
    public async Task<IActionResult> DeleteAsset(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var exists = await _assetRepository.ExistsAsync(a => a.Id == id, cancellationToken);
            
            if (!exists)
            {
                _logger.LogWarning("Asset with ID {AssetId} not found for deletion", id);
                return NotFound($"Asset with ID {id} not found");
            }

            var deleted = await _assetRepository.SoftDeleteAsync(id, User.Identity?.Name, cancellationToken);
            
            if (deleted)
            {
                _logger.LogInformation("Deleted asset {AssetId}", id);
                return NoContent();
            }
            
            return StatusCode(500, "Failed to delete asset");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting asset {AssetId}", id);
            return StatusCode(500, "An error occurred while deleting the asset");
        }
    }

    /// <summary>
    /// Gets assets by type
    /// </summary>
    /// <param name="type">Asset type</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of assets of the specified type</returns>
    [HttpGet("by-type/{type}")]
    [ProducesResponseType(typeof(IEnumerable<AssetDto>), 200)]
    public async Task<ActionResult<IEnumerable<AssetDto>>> GetAssetsByType(string type, CancellationToken cancellationToken)
    {
        try
        {
            var assets = await _assetRepository.GetByTypeAsync(type, cancellationToken);
            var assetDtos = _mapper.Map<IEnumerable<AssetDto>>(assets);
            
            _logger.LogInformation("Retrieved {Count} assets of type {AssetType}", assetDtos.Count(), type);
            return Ok(assetDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving assets by type {AssetType}", type);
            return StatusCode(500, "An error occurred while retrieving assets");
        }
    }

    /// <summary>
    /// Gets assets by status
    /// </summary>
    /// <param name="status">Asset status</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of assets with the specified status</returns>
    [HttpGet("by-status/{status}")]
    [ProducesResponseType(typeof(IEnumerable<AssetDto>), 200)]
    public async Task<ActionResult<IEnumerable<AssetDto>>> GetAssetsByStatus(AssetStatus status, CancellationToken cancellationToken)
    {
        try
        {
            var assets = await _assetRepository.GetByStatusAsync(status, cancellationToken);
            var assetDtos = _mapper.Map<IEnumerable<AssetDto>>(assets);
            
            _logger.LogInformation("Retrieved {Count} assets with status {AssetStatus}", assetDtos.Count(), status);
            return Ok(assetDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving assets by status {AssetStatus}", status);
            return StatusCode(500, "An error occurred while retrieving assets");
        }
    }

    /// <summary>
    /// Gets child assets of a parent asset
    /// </summary>
    /// <param name="parentId">Parent asset ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of child assets</returns>
    [HttpGet("{parentId:guid}/children")]
    [ProducesResponseType(typeof(IEnumerable<AssetDto>), 200)]
    public async Task<ActionResult<IEnumerable<AssetDto>>> GetChildAssets(Guid parentId, CancellationToken cancellationToken)
    {
        try
        {
            var assets = await _assetRepository.GetChildAssetsAsync(parentId, cancellationToken);
            var assetDtos = _mapper.Map<IEnumerable<AssetDto>>(assets);
            
            _logger.LogInformation("Retrieved {Count} child assets for parent {ParentId}", assetDtos.Count(), parentId);
            return Ok(assetDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving child assets for parent {ParentId}", parentId);
            return StatusCode(500, "An error occurred while retrieving child assets");
        }
    }

    /// <summary>
    /// Searches assets by name, description, or tags
    /// </summary>
    /// <param name="query">Search query</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching assets</returns>
    [HttpGet("search")]
    [ProducesResponseType(typeof(IEnumerable<AssetDto>), 200)]
    public async Task<ActionResult<IEnumerable<AssetDto>>> SearchAssets([FromQuery] string query, CancellationToken cancellationToken)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest("Search query cannot be empty");
            }

            var assets = await _assetRepository.SearchAsync(query, cancellationToken);
            var assetDtos = _mapper.Map<IEnumerable<AssetDto>>(assets);
            
            _logger.LogInformation("Found {Count} assets matching query '{Query}'", assetDtos.Count(), query);
            return Ok(assetDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching assets with query '{Query}'", query);
            return StatusCode(500, "An error occurred while searching assets");
        }
    }

    /// <summary>
    /// Gets asset statistics
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Asset statistics</returns>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(AssetStatisticsDto), 200)]
    public async Task<ActionResult<AssetStatisticsDto>> GetAssetStatistics(CancellationToken cancellationToken)
    {
        try
        {
            var typeStats = await _assetRepository.GetAssetStatisticsByTypeAsync(cancellationToken);
            var statusStats = await _assetRepository.GetAssetStatisticsByStatusAsync(cancellationToken);
            var totalCount = await _assetRepository.CountAsync(cancellationToken: cancellationToken);

            var statistics = new AssetStatisticsDto
            {
                TotalAssets = totalCount,
                AssetsByType = typeStats,
                AssetsByStatus = statusStats.ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value)
            };
            
            _logger.LogInformation("Retrieved asset statistics: {TotalAssets} total assets", totalCount);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving asset statistics");
            return StatusCode(500, "An error occurred while retrieving asset statistics");
        }
    }
}

/// <summary>
/// DTO for creating a new asset
/// </summary>
public class CreateAssetDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string AssetType { get; set; } = string.Empty;
    public string? Manufacturer { get; set; }
    public string? Model { get; set; }
    public string? SerialNumber { get; set; }
    public AssetStatus Status { get; set; } = AssetStatus.Unknown;
    public LocationDto? Location { get; set; }
    public Guid? ParentAssetId { get; set; }
    public int CriticalityLevel { get; set; } = 3;
    public DateTimeOffset? InstallationDate { get; set; }
    public DateTimeOffset? NextMaintenanceDate { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public ICollection<string> Tags { get; set; } = new List<string>();
}

/// <summary>
/// DTO for updating an existing asset
/// </summary>
public class UpdateAssetDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string AssetType { get; set; } = string.Empty;
    public string? Manufacturer { get; set; }
    public string? Model { get; set; }
    public string? SerialNumber { get; set; }
    public AssetStatus Status { get; set; }
    public LocationDto? Location { get; set; }
    public Guid? ParentAssetId { get; set; }
    public int CriticalityLevel { get; set; }
    public DateTimeOffset? InstallationDate { get; set; }
    public DateTimeOffset? LastMaintenanceDate { get; set; }
    public DateTimeOffset? NextMaintenanceDate { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public ICollection<string> Tags { get; set; } = new List<string>();
}

/// <summary>
/// DTO for asset statistics
/// </summary>
public class AssetStatisticsDto
{
    public int TotalAssets { get; set; }
    public Dictionary<string, int> AssetsByType { get; set; } = new();
    public Dictionary<string, int> AssetsByStatus { get; set; } = new();
}
