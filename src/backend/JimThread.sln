Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{B2C3D4E5-F6G7-8901-BCDE-F23456789012}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{C3D4E5F6-G7H8-9012-CDEF-************}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{D4E5F6G7-H8I9-0123-DEF0-************}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Core", "JimThread.Core\JimThread.Core.csproj", "{E5F6G7H8-I9J0-1234-EF01-567890123456}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Infrastructure", "JimThread.Infrastructure\JimThread.Infrastructure.csproj", "{F6G7H8I9-J0K1-2345-F012-678901234567}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Api", "JimThread.Api\JimThread.Api.csproj", "{G7H8I9J0-K1L2-3456-0123-789012345678}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Analytics", "JimThread.Analytics\JimThread.Analytics.csproj", "{H8I9J0K1-L2M3-4567-1234-890123456789}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Automation", "JimThread.Automation\JimThread.Automation.csproj", "{I9J0K1L2-M3N4-5678-2345-************}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Discovery", "JimThread.Discovery\JimThread.Discovery.csproj", "{J0K1L2M3-N4O5-6789-3456-012345678901}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Connectors", "JimThread.Connectors\JimThread.Connectors.csproj", "{K1L2M3N4-O5P6-7890-4567-123456789012}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Core.Tests", "JimThread.Core.Tests\JimThread.Core.Tests.csproj", "{L2M3N4O5-P6Q7-8901-5678-************}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "JimThread.Api.Tests", "JimThread.Api.Tests\JimThread.Api.Tests.csproj", "{M3N4O5P6-Q7R8-9012-6789-************}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F6G7H8-I9J0-1234-EF01-567890123456}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6G7H8I9-J0K1-2345-F012-678901234567}.Release|Any CPU.Build.0 = Release|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{G7H8I9J0-K1L2-3456-0123-789012345678}.Release|Any CPU.Build.0 = Release|Any CPU
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{H8I9J0K1-L2M3-4567-1234-890123456789}.Release|Any CPU.Build.0 = Release|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{I9J0K1L2-M3N4-5678-2345-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{J0K1L2M3-N4O5-6789-3456-012345678901}.Release|Any CPU.Build.0 = Release|Any CPU
		{K1L2M3N4-O5P6-7890-4567-123456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{K1L2M3N4-O5P6-7890-4567-123456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{K1L2M3N4-O5P6-7890-4567-123456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{K1L2M3N4-O5P6-7890-4567-123456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{L2M3N4O5-P6Q7-8901-5678-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{L2M3N4O5-P6Q7-8901-5678-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{L2M3N4O5-P6Q7-8901-5678-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{L2M3N4O5-P6Q7-8901-5678-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{M3N4O5P6-Q7R8-9012-6789-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{M3N4O5P6-Q7R8-9012-6789-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{M3N4O5P6-Q7R8-9012-6789-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{M3N4O5P6-Q7R8-9012-6789-************}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E5F6G7H8-I9J0-1234-EF01-567890123456} = {A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
		{F6G7H8I9-J0K1-2345-F012-678901234567} = {C3D4E5F6-G7H8-9012-CDEF-************}
		{G7H8I9J0-K1L2-3456-0123-789012345678} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{H8I9J0K1-L2M3-4567-1234-890123456789} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{I9J0K1L2-M3N4-5678-2345-************} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{J0K1L2M3-N4O5-6789-3456-012345678901} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{K1L2M3N4-O5P6-7890-4567-123456789012} = {B2C3D4E5-F6G7-8901-BCDE-F23456789012}
		{L2M3N4O5-P6Q7-8901-5678-************} = {D4E5F6G7-H8I9-0123-DEF0-************}
		{M3N4O5P6-Q7R8-9012-6789-************} = {D4E5F6G7-H8I9-0123-DEF0-************}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
