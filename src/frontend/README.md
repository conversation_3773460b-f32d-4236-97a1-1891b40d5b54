# JimThread Frontend

A modern React TypeScript frontend for the JimThread Operational Intelligence Platform.

## Features

- **Modern React 18** with TypeScript for type safety
- **Material-UI (MUI)** for consistent, professional UI components
- **React Query** for efficient data fetching and caching
- **React Router** for client-side routing
- **Recharts** for data visualization and charts
- **React Hook Form** for form management
- **Responsive Design** that works on desktop and mobile

## Architecture

### Key Components

- **Layout**: Main application layout with navigation sidebar
- **Dashboard**: Overview with key metrics and charts
- **Assets**: Asset management with CRUD operations
- **Sensors**: Sensor monitoring and management
- **Analytics**: Future analytics and ML features
- **Automation**: Future automation capabilities
- **Settings**: System configuration and health monitoring

### Data Flow

```
API Layer → React Query → Components → UI
     ↓
Mock Data Fallback (when API unavailable)
```

### State Management

- **React Query** for server state management
- **React Hook Form** for form state
- **React Context** for global UI state (if needed)

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Backend API running on `https://localhost:5001` (or configure proxy)

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm start
```

The application will open at `http://localhost:3000`.

### Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## Configuration

### Environment Variables

Create a `.env` file in the frontend directory:

```env
REACT_APP_API_BASE_URL=https://localhost:5001/api/v1
```

### API Integration

The frontend automatically falls back to mock data when the backend API is unavailable, allowing for development and testing without a running backend.

## Features Overview

### Dashboard
- System overview with key metrics
- Asset and sensor statistics
- Health monitoring
- Interactive charts and visualizations

### Asset Management
- View all assets in a data grid
- Create, edit, and delete assets
- Search and filter capabilities
- Asset hierarchy visualization
- Location and maintenance tracking

### Sensor Monitoring
- Real-time sensor data display
- Status monitoring
- Data quality indicators
- Search and filtering

### Future Features
- **Analytics**: Anomaly detection, predictive maintenance
- **Automation**: Automated responses, process optimization
- **Advanced Visualizations**: 3D models, digital twins
- **Real-time Updates**: WebSocket integration
- **Mobile App**: React Native companion app

## Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow React best practices and hooks patterns
- Use Material-UI components consistently
- Implement responsive design principles

### Component Structure
```
src/
├── components/     # Reusable UI components
├── pages/         # Page-level components
├── services/      # API services and utilities
├── types/         # TypeScript type definitions
├── hooks/         # Custom React hooks
└── utils/         # Utility functions
```

### API Integration
- Use React Query for all API calls
- Implement proper error handling
- Provide loading states
- Include mock data fallbacks

## Testing

### Unit Tests
```bash
npm test
```

### E2E Tests (Future)
- Cypress for end-to-end testing
- Test critical user workflows
- Automated testing in CI/CD

## Deployment

### Production Build
```bash
npm run build
```

### Docker Deployment
The frontend can be served using the nginx container defined in the docker-compose.yml.

### Environment-Specific Builds
- Development: Hot reload, debug tools
- Staging: Production build with staging API
- Production: Optimized build with production API

## Performance Optimization

- **Code Splitting**: Lazy load pages and components
- **Caching**: React Query for API response caching
- **Bundle Optimization**: Tree shaking and minification
- **Image Optimization**: WebP format and lazy loading
- **CDN**: Serve static assets from CDN

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Follow the established code style
2. Write tests for new features
3. Update documentation
4. Use semantic commit messages
5. Create pull requests for review

## Troubleshooting

### Common Issues

**API Connection Failed**
- Check if backend is running on `https://localhost:5001`
- Verify CORS configuration
- Check network connectivity

**Build Errors**
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check TypeScript errors: `npm run type-check`
- Verify all dependencies are compatible

**Performance Issues**
- Check React DevTools for unnecessary re-renders
- Verify React Query cache configuration
- Monitor bundle size with webpack-bundle-analyzer

## License

This project is part of the JimThread platform and follows the same licensing terms.
