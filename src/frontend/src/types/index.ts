// Asset types
export interface Asset {
  id: string;
  name: string;
  description?: string;
  assetType: string;
  manufacturer?: string;
  model?: string;
  serialNumber?: string;
  status: AssetStatus;
  location?: Location;
  parentAssetId?: string;
  parentAssetName?: string;
  childAssets: Asset[];
  sensors: Sensor[];
  metadata: Record<string, any>;
  tags: string[];
  criticalityLevel: number;
  installationDate?: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export enum AssetStatus {
  Unknown = 'Unknown',
  Online = 'Online',
  Offline = 'Offline',
  Maintenance = 'Maintenance',
  Error = 'Error',
  Warning = 'Warning',
  Decommissioned = 'Decommissioned'
}

// Sensor types
export interface Sensor {
  id: string;
  tag: string;
  name: string;
  description?: string;
  sensorType: string;
  unit: string;
  dataType: SensorDataType;
  minValue?: number;
  maxValue?: number;
  precision: number;
  samplingRateSeconds: number;
  assetId: string;
  assetName?: string;
  status: SensorStatus;
  lastValue?: number;
  lastReadingAt?: string;
  lastReadingQuality: DataQuality;
  alarmConfiguration?: AlarmConfiguration;
  metadata: Record<string, any>;
  dataSource?: DataSource;
  enableAnomalyDetection: boolean;
  enableArchiving: boolean;
  retentionDays: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export enum SensorDataType {
  Numeric = 'Numeric',
  Boolean = 'Boolean',
  Text = 'Text',
  Enum = 'Enum'
}

export enum SensorStatus {
  Unknown = 'Unknown',
  Online = 'Online',
  Offline = 'Offline',
  Error = 'Error',
  Maintenance = 'Maintenance',
  Calibration = 'Calibration'
}

export enum DataQuality {
  Unknown = 'Unknown',
  Good = 'Good',
  Bad = 'Bad',
  Uncertain = 'Uncertain',
  Stale = 'Stale'
}

// Location type
export interface Location {
  building?: string;
  floor?: string;
  room?: string;
  zone?: string;
  latitude?: number;
  longitude?: number;
  elevation?: number;
  address?: string;
}

// Alarm Configuration
export interface AlarmConfiguration {
  highHighThreshold?: number;
  highThreshold?: number;
  lowThreshold?: number;
  lowLowThreshold?: number;
  deadband: number;
  delaySeconds: number;
  alarmsEnabled: boolean;
  priority: number;
  autoAcknowledge: boolean;
  alarmMessage?: string;
}

// Data Source
export interface DataSource {
  sourceType: string;
  connectionString: string;
  sourceTag: string;
  serverName?: string;
  database?: string;
  configuration: Record<string, string>;
  isActive: boolean;
  lastConnectionAt?: string;
  lastError?: string;
}

// API DTOs
export interface CreateAssetDto {
  name: string;
  description?: string;
  assetType: string;
  manufacturer?: string;
  model?: string;
  serialNumber?: string;
  status: AssetStatus;
  location?: Location;
  parentAssetId?: string;
  criticalityLevel: number;
  installationDate?: string;
  nextMaintenanceDate?: string;
  metadata: Record<string, any>;
  tags: string[];
}

export interface UpdateAssetDto {
  name: string;
  description?: string;
  assetType: string;
  manufacturer?: string;
  model?: string;
  serialNumber?: string;
  status: AssetStatus;
  location?: Location;
  parentAssetId?: string;
  criticalityLevel: number;
  installationDate?: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  metadata: Record<string, any>;
  tags: string[];
}

export interface CreateSensorDto {
  tag: string;
  name: string;
  description?: string;
  sensorType: string;
  unit: string;
  dataType: SensorDataType;
  minValue?: number;
  maxValue?: number;
  precision: number;
  samplingRateSeconds: number;
  assetId: string;
  status: SensorStatus;
  alarmConfiguration?: AlarmConfiguration;
  metadata: Record<string, any>;
  dataSource?: DataSource;
  enableAnomalyDetection: boolean;
  enableArchiving: boolean;
  retentionDays: number;
}

// Statistics
export interface AssetStatistics {
  totalAssets: number;
  assetsByType: Record<string, number>;
  assetsByStatus: Record<string, number>;
}

export interface SensorStatistics {
  totalSensors: number;
  sensorsByType: Record<string, number>;
  sensorsByStatus: Record<string, number>;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

// Health Check
export interface HealthCheck {
  status: string;
  checks: HealthCheckItem[];
  duration: string;
}

export interface HealthCheckItem {
  name: string;
  status: string;
  exception?: string;
  duration: string;
}

// Dashboard types
export interface DashboardMetrics {
  totalAssets: number;
  onlineAssets: number;
  totalSensors: number;
  onlineSensors: number;
  activeAlerts: number;
  systemHealth: number;
}

export interface ChartDataPoint {
  name: string;
  value: number;
  color?: string;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'date' | 'checkbox';
  required?: boolean;
  options?: { value: string; label: string }[];
  validation?: any;
}

// Navigation
export interface NavigationItem {
  path: string;
  label: string;
  icon: React.ComponentType;
  children?: NavigationItem[];
}
