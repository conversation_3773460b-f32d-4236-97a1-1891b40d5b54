body {
  margin: 0;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Custom styles for data grid */
.MuiDataGrid-root {
  border: none !important;
}

.MuiDataGrid-columnHeaders {
  background-color: #f5f5f5;
  border-bottom: 2px solid #e0e0e0;
}

.MuiDataGrid-cell {
  border-bottom: 1px solid #f0f0f0;
}

.MuiDataGrid-row:hover {
  background-color: #f9f9f9;
}

/* Status chip styles */
.status-chip {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 0.75rem;
}

.status-online {
  background-color: #4caf50 !important;
  color: white !important;
}

.status-offline {
  background-color: #f44336 !important;
  color: white !important;
}

.status-maintenance {
  background-color: #ff9800 !important;
  color: white !important;
}

.status-error {
  background-color: #e91e63 !important;
  color: white !important;
}

.status-warning {
  background-color: #ff5722 !important;
  color: white !important;
}

.status-unknown {
  background-color: #9e9e9e !important;
  color: white !important;
}

/* Criticality level styles */
.criticality-1 {
  color: #4caf50;
  font-weight: 500;
}

.criticality-2 {
  color: #8bc34a;
  font-weight: 500;
}

.criticality-3 {
  color: #ff9800;
  font-weight: 500;
}

.criticality-4 {
  color: #ff5722;
  font-weight: 500;
}

.criticality-5 {
  color: #f44336;
  font-weight: 600;
}

/* Form styles */
.form-section {
  margin-bottom: 24px;
}

.form-section-title {
  margin-bottom: 16px;
  color: #333;
  font-weight: 500;
}

/* Dashboard card styles */
.dashboard-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-card .MuiCardContent-root {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Chart container */
.chart-container {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .MuiDrawer-paper {
    width: 240px;
  }
  
  .chart-container {
    height: 250px;
  }
}

/* Error and success states */
.error-state {
  color: #f44336;
  text-align: center;
  padding: 20px;
}

.success-state {
  color: #4caf50;
  text-align: center;
  padding: 20px;
}

.empty-state {
  color: #666;
  text-align: center;
  padding: 40px 20px;
}

.empty-state-icon {
  font-size: 48px !important;
  color: #ccc;
  margin-bottom: 16px;
}
