import axios, { AxiosResponse } from 'axios';
import {
  Asset,
  Sensor,
  CreateAssetDto,
  UpdateAssetDto,
  CreateSensorDto,
  AssetStatistics,
  SensorStatistics,
  HealthCheck,
  AssetStatus,
  SensorStatus,
} from '../types';

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Asset API
export const assetApi = {
  // Get all assets
  getAll: (): Promise<AxiosResponse<Asset[]>> =>
    api.get('/assets'),

  // Get asset by ID
  getById: (id: string): Promise<AxiosResponse<Asset>> =>
    api.get(`/assets/${id}`),

  // Create new asset
  create: (asset: CreateAssetDto): Promise<AxiosResponse<Asset>> =>
    api.post('/assets', asset),

  // Update asset
  update: (id: string, asset: UpdateAssetDto): Promise<AxiosResponse<Asset>> =>
    api.put(`/assets/${id}`, asset),

  // Delete asset
  delete: (id: string): Promise<AxiosResponse<void>> =>
    api.delete(`/assets/${id}`),

  // Get assets by type
  getByType: (type: string): Promise<AxiosResponse<Asset[]>> =>
    api.get(`/assets/by-type/${type}`),

  // Get assets by status
  getByStatus: (status: AssetStatus): Promise<AxiosResponse<Asset[]>> =>
    api.get(`/assets/by-status/${status}`),

  // Get child assets
  getChildren: (parentId: string): Promise<AxiosResponse<Asset[]>> =>
    api.get(`/assets/${parentId}/children`),

  // Search assets
  search: (query: string): Promise<AxiosResponse<Asset[]>> =>
    api.get(`/assets/search?query=${encodeURIComponent(query)}`),

  // Get asset statistics
  getStatistics: (): Promise<AxiosResponse<AssetStatistics>> =>
    api.get('/assets/statistics'),
};

// Sensor API
export const sensorApi = {
  // Get all sensors
  getAll: (): Promise<AxiosResponse<Sensor[]>> =>
    api.get('/sensors'),

  // Get sensor by ID
  getById: (id: string): Promise<AxiosResponse<Sensor>> =>
    api.get(`/sensors/${id}`),

  // Create new sensor
  create: (sensor: CreateSensorDto): Promise<AxiosResponse<Sensor>> =>
    api.post('/sensors', sensor),

  // Update sensor
  update: (id: string, sensor: Partial<Sensor>): Promise<AxiosResponse<Sensor>> =>
    api.put(`/sensors/${id}`, sensor),

  // Delete sensor
  delete: (id: string): Promise<AxiosResponse<void>> =>
    api.delete(`/sensors/${id}`),

  // Get sensors by asset ID
  getByAssetId: (assetId: string): Promise<AxiosResponse<Sensor[]>> =>
    api.get(`/sensors/by-asset/${assetId}`),

  // Get sensors by type
  getByType: (type: string): Promise<AxiosResponse<Sensor[]>> =>
    api.get(`/sensors/by-type/${type}`),

  // Get sensors by status
  getByStatus: (status: SensorStatus): Promise<AxiosResponse<Sensor[]>> =>
    api.get(`/sensors/by-status/${status}`),

  // Get sensor by tag
  getByTag: (tag: string): Promise<AxiosResponse<Sensor>> =>
    api.get(`/sensors/by-tag/${tag}`),

  // Search sensors
  search: (query: string): Promise<AxiosResponse<Sensor[]>> =>
    api.get(`/sensors/search?query=${encodeURIComponent(query)}`),

  // Get sensor statistics
  getStatistics: (): Promise<AxiosResponse<SensorStatistics>> =>
    api.get('/sensors/statistics'),
};

// Health API
export const healthApi = {
  // Get health status
  getHealth: (): Promise<AxiosResponse<HealthCheck>> =>
    api.get('/health'),

  // Get readiness status
  getReady: (): Promise<AxiosResponse<HealthCheck>> =>
    api.get('/health/ready'),

  // Get liveness status
  getLive: (): Promise<AxiosResponse<HealthCheck>> =>
    api.get('/health/live'),
};

// System API
export const systemApi = {
  // Get system info
  getInfo: (): Promise<AxiosResponse<any>> =>
    api.get('/'),
};

// Utility functions
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.response?.data?.error?.message) {
    return error.response.data.error.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export const isApiError = (error: any): boolean => {
  return error.response !== undefined;
};

// Mock data for development (when backend is not available)
export const mockData = {
  assets: [
    {
      id: '1',
      name: 'Main Pump A',
      description: 'Primary water circulation pump',
      assetType: 'Pump',
      manufacturer: 'Grundfos',
      model: 'CR 32-4',
      serialNumber: 'GF-2023-001',
      status: 'Online' as AssetStatus,
      location: {
        building: 'Plant A',
        floor: 'Ground',
        room: 'Pump Room 1',
      },
      criticalityLevel: 5,
      installationDate: '2023-01-15T00:00:00Z',
      nextMaintenanceDate: '2024-01-15T00:00:00Z',
      createdAt: '2023-01-15T10:00:00Z',
      updatedAt: '2023-12-01T14:30:00Z',
      childAssets: [],
      sensors: [],
      metadata: {},
      tags: ['critical', 'water-system'],
    },
    {
      id: '2',
      name: 'Conveyor Belt B',
      description: 'Main production line conveyor',
      assetType: 'Conveyor',
      manufacturer: 'Siemens',
      model: 'CONV-2000',
      serialNumber: 'SI-2023-002',
      status: 'Maintenance' as AssetStatus,
      location: {
        building: 'Plant A',
        floor: 'Ground',
        room: 'Production Line 1',
      },
      criticalityLevel: 4,
      installationDate: '2023-02-01T00:00:00Z',
      nextMaintenanceDate: '2024-02-01T00:00:00Z',
      createdAt: '2023-02-01T10:00:00Z',
      updatedAt: '2023-12-01T14:30:00Z',
      childAssets: [],
      sensors: [],
      metadata: {},
      tags: ['production', 'conveyor'],
    },
  ] as Asset[],

  sensors: [
    {
      id: '1',
      tag: 'TEMP_001',
      name: 'Pump A Temperature',
      description: 'Temperature sensor for main pump A',
      sensorType: 'Temperature',
      unit: '°C',
      dataType: 'Numeric' as any,
      precision: 1,
      samplingRateSeconds: 30,
      assetId: '1',
      assetName: 'Main Pump A',
      status: 'Online' as SensorStatus,
      lastValue: 45.2,
      lastReadingAt: '2023-12-01T14:30:00Z',
      lastReadingQuality: 'Good' as any,
      enableAnomalyDetection: true,
      enableArchiving: true,
      retentionDays: 365,
      createdAt: '2023-01-15T10:00:00Z',
      updatedAt: '2023-12-01T14:30:00Z',
      metadata: {},
    },
    {
      id: '2',
      tag: 'VIBR_001',
      name: 'Pump A Vibration',
      description: 'Vibration sensor for main pump A',
      sensorType: 'Vibration',
      unit: 'mm/s',
      dataType: 'Numeric' as any,
      precision: 2,
      samplingRateSeconds: 10,
      assetId: '1',
      assetName: 'Main Pump A',
      status: 'Online' as SensorStatus,
      lastValue: 2.1,
      lastReadingAt: '2023-12-01T14:30:00Z',
      lastReadingQuality: 'Good' as any,
      enableAnomalyDetection: true,
      enableArchiving: true,
      retentionDays: 365,
      createdAt: '2023-01-15T10:00:00Z',
      updatedAt: '2023-12-01T14:30:00Z',
      metadata: {},
    },
  ] as Sensor[],

  statistics: {
    assets: {
      totalAssets: 2,
      assetsByType: {
        'Pump': 1,
        'Conveyor': 1,
      },
      assetsByStatus: {
        'Online': 1,
        'Maintenance': 1,
      },
    } as AssetStatistics,
    sensors: {
      totalSensors: 2,
      sensorsByType: {
        'Temperature': 1,
        'Vibration': 1,
      },
      sensorsByStatus: {
        'Online': 2,
      },
    } as SensorStatistics,
  },
};

export default api;
