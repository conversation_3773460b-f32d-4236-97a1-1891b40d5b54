import React, { useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Typography,
  Divider,
  CircularProgress,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { Asset, AssetStatus, CreateAssetDto } from '../types';

interface AssetFormProps {
  asset?: Asset | null;
  onSubmit: (data: CreateAssetDto) => void;
  onCancel: () => void;
  readOnly?: boolean;
  isLoading?: boolean;
}

const assetTypes = [
  'Pump',
  'Motor',
  'Conveyor',
  'Compressor',
  'Heat Exchanger',
  'Tank',
  'Valve',
  'Sensor',
  'Controller',
  'Other',
];

const manufacturers = [
  'Siemens',
  'ABB',
  'Schneider Electric',
  'Grundfos',
  'Danfoss',
  'Emerson',
  'Honeywell',
  'Rockwell Automation',
  'Other',
];

export const AssetForm: React.FC<AssetFormProps> = ({
  asset,
  onSubmit,
  onCancel,
  readOnly = false,
  isLoading = false,
}) => {
  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateAssetDto>({
    defaultValues: {
      name: '',
      description: '',
      assetType: '',
      manufacturer: '',
      model: '',
      serialNumber: '',
      status: AssetStatus.Unknown,
      location: {
        building: '',
        floor: '',
        room: '',
        zone: '',
      },
      parentAssetId: '',
      criticalityLevel: 3,
      installationDate: '',
      nextMaintenanceDate: '',
      metadata: {},
      tags: [],
    },
  });

  const watchedTags = watch('tags') || [];

  useEffect(() => {
    if (asset) {
      reset({
        name: asset.name,
        description: asset.description || '',
        assetType: asset.assetType,
        manufacturer: asset.manufacturer || '',
        model: asset.model || '',
        serialNumber: asset.serialNumber || '',
        status: asset.status,
        location: asset.location || {
          building: '',
          floor: '',
          room: '',
          zone: '',
        },
        parentAssetId: asset.parentAssetId || '',
        criticalityLevel: asset.criticalityLevel,
        installationDate: asset.installationDate ? asset.installationDate.split('T')[0] : '',
        nextMaintenanceDate: asset.nextMaintenanceDate ? asset.nextMaintenanceDate.split('T')[0] : '',
        metadata: asset.metadata || {},
        tags: asset.tags || [],
      });
    }
  }, [asset, reset]);

  const handleFormSubmit = (data: CreateAssetDto) => {
    // Convert date strings to ISO format
    const formattedData = {
      ...data,
      installationDate: data.installationDate ? new Date(data.installationDate).toISOString() : undefined,
      nextMaintenanceDate: data.nextMaintenanceDate ? new Date(data.nextMaintenanceDate).toISOString() : undefined,
    };
    onSubmit(formattedData);
  };

  const handleAddTag = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      const target = event.target as HTMLInputElement;
      const newTag = target.value.trim();
      if (newTag && !watchedTags.includes(newTag)) {
        setValue('tags', [...watchedTags, newTag]);
        target.value = '';
      }
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove));
  };

  return (
    <Box component="form" onSubmit={handleSubmit(handleFormSubmit)} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Basic Information
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="name"
            control={control}
            rules={{ required: 'Asset name is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Asset Name"
                error={!!errors.name}
                helperText={errors.name?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="assetType"
            control={control}
            rules={{ required: 'Asset type is required' }}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.assetType} disabled={readOnly}>
                <InputLabel>Asset Type</InputLabel>
                <Select {...field} label="Asset Type">
                  {assetTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Description"
                multiline
                rows={3}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        {/* Technical Details */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Technical Details
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={4}>
          <Controller
            name="manufacturer"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth disabled={readOnly}>
                <InputLabel>Manufacturer</InputLabel>
                <Select {...field} label="Manufacturer">
                  {manufacturers.map((manufacturer) => (
                    <MenuItem key={manufacturer} value={manufacturer}>
                      {manufacturer}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <Controller
            name="model"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Model"
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <Controller
            name="serialNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Serial Number"
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        {/* Status and Criticality */}
        <Grid item xs={12} md={6}>
          <Controller
            name="status"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth disabled={readOnly}>
                <InputLabel>Status</InputLabel>
                <Select {...field} label="Status">
                  {Object.values(AssetStatus).map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="criticalityLevel"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth disabled={readOnly}>
                <InputLabel>Criticality Level</InputLabel>
                <Select {...field} label="Criticality Level">
                  {[1, 2, 3, 4, 5].map((level) => (
                    <MenuItem key={level} value={level}>
                      Level {level} {level >= 4 ? '(Critical)' : level === 3 ? '(Medium)' : '(Low)'}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>

        {/* Location */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Location
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={3}>
          <Controller
            name="location.building"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Building"
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={3}>
          <Controller
            name="location.floor"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Floor"
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={3}>
          <Controller
            name="location.room"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Room"
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={3}>
          <Controller
            name="location.zone"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Zone"
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        {/* Dates */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Maintenance Schedule
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="installationDate"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Installation Date"
                type="date"
                InputLabelProps={{ shrink: true }}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="nextMaintenanceDate"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Next Maintenance Date"
                type="date"
                InputLabelProps={{ shrink: true }}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        {/* Tags */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Tags
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Add Tags (Press Enter to add)"
            placeholder="Type a tag and press Enter"
            onKeyDown={handleAddTag}
            disabled={readOnly}
          />
          <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {watchedTags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                onDelete={readOnly ? undefined : () => handleRemoveTag(tag)}
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
        </Grid>

        {/* Form Actions */}
        {!readOnly && (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3 }}>
              <Button onClick={onCancel} disabled={isLoading}>
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} /> : null}
              >
                {asset ? 'Update Asset' : 'Create Asset'}
              </Button>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};
