import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { assetApi, mockData, handleApiError } from '../services/api';
import { Asset, AssetStatus, CreateAssetDto } from '../types';
import { AssetForm } from '../components/AssetForm';

export const Assets: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isViewMode, setIsViewMode] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<Asset | null>(null);

  const queryClient = useQueryClient();

  // Fetch assets with fallback to mock data
  const { data: assets, isLoading, error, refetch } = useQuery({
    queryKey: ['assets'],
    queryFn: () => assetApi.getAll(),
    retry: false,
  });

  // Create asset mutation
  const createAssetMutation = useMutation({
    mutationFn: (asset: CreateAssetDto) => assetApi.create(asset),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assets'] });
      setIsFormOpen(false);
      setSelectedAsset(null);
    },
  });

  // Update asset mutation
  const updateAssetMutation = useMutation({
    mutationFn: ({ id, asset }: { id: string; asset: any }) => assetApi.update(id, asset),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assets'] });
      setIsFormOpen(false);
      setSelectedAsset(null);
    },
  });

  // Delete asset mutation
  const deleteAssetMutation = useMutation({
    mutationFn: (id: string) => assetApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assets'] });
      setDeleteConfirmOpen(false);
      setAssetToDelete(null);
    },
  });

  const assetsData = assets?.data || mockData.assets;

  // Filter assets based on search query
  const filteredAssets = assetsData.filter(asset =>
    asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.assetType.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.manufacturer?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    asset.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const getStatusColor = (status: AssetStatus) => {
    switch (status) {
      case AssetStatus.Online:
        return 'success';
      case AssetStatus.Offline:
        return 'error';
      case AssetStatus.Maintenance:
        return 'warning';
      case AssetStatus.Error:
        return 'error';
      case AssetStatus.Warning:
        return 'warning';
      default:
        return 'default';
    }
  };

  const getCriticalityColor = (level: number) => {
    if (level >= 4) return 'error';
    if (level === 3) return 'warning';
    return 'success';
  };

  const handleCreateAsset = () => {
    setSelectedAsset(null);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleEditAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleViewAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setIsViewMode(true);
    setIsFormOpen(true);
  };

  const handleDeleteAsset = (asset: Asset) => {
    setAssetToDelete(asset);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (assetToDelete) {
      deleteAssetMutation.mutate(assetToDelete.id);
    }
  };

  const handleFormSubmit = (formData: any) => {
    if (selectedAsset) {
      updateAssetMutation.mutate({ id: selectedAsset.id, asset: formData });
    } else {
      createAssetMutation.mutate(formData);
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Name',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2" fontWeight={600}>
            {params.value}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.assetType}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
          variant="filled"
        />
      ),
    },
    {
      field: 'criticalityLevel',
      headerName: 'Criticality',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={`Level ${params.value}`}
          color={getCriticalityColor(params.value)}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'manufacturer',
      headerName: 'Manufacturer',
      width: 150,
    },
    {
      field: 'model',
      headerName: 'Model',
      width: 150,
    },
    {
      field: 'location',
      headerName: 'Location',
      width: 200,
      renderCell: (params) => {
        const location = params.value;
        if (!location) return '-';
        const parts = [location.building, location.floor, location.room].filter(Boolean);
        return parts.join(' / ') || '-';
      },
    },
    {
      field: 'tags',
      headerName: 'Tags',
      width: 200,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
          {params.value?.slice(0, 2).map((tag: string, index: number) => (
            <Chip key={index} label={tag} size="small" variant="outlined" />
          ))}
          {params.value?.length > 2 && (
            <Chip label={`+${params.value.length - 2}`} size="small" variant="outlined" />
          )}
        </Box>
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<ViewIcon />}
          label="View"
          onClick={() => handleViewAsset(params.row)}
        />,
        <GridActionsCellItem
          icon={<EditIcon />}
          label="Edit"
          onClick={() => handleEditAsset(params.row)}
        />,
        <GridActionsCellItem
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => handleDeleteAsset(params.row)}
        />,
      ],
    },
  ];

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Assets
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Refresh">
            <IconButton onClick={() => refetch()}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateAsset}
          >
            Add Asset
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {handleApiError(error)} - Showing mock data for demonstration.
        </Alert>
      )}

      {/* Search */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search assets by name, type, manufacturer, or tags..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Data Grid */}
      <Box sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredAssets}
          columns={columns}
          pageSize={25}
          rowsPerPageOptions={[25, 50, 100]}
          disableSelectionOnClick
          sx={{
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid #f0f0f0',
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f8f9fa',
              borderBottom: '2px solid #e0e0e0',
            },
          }}
        />
      </Box>

      {/* Asset Form Dialog */}
      <Dialog
        open={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {isViewMode ? 'View Asset' : selectedAsset ? 'Edit Asset' : 'Create Asset'}
        </DialogTitle>
        <DialogContent>
          <AssetForm
            asset={selectedAsset}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            readOnly={isViewMode}
            isLoading={createAssetMutation.isPending || updateAssetMutation.isPending}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the asset "{assetToDelete?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            disabled={deleteAssetMutation.isPending}
          >
            {deleteAssetMutation.isPending ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
