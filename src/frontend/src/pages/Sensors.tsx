import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useQuery } from '@tanstack/react-query';
import { sensorApi, mockData, handleApiError } from '../services/api';
import { Sensor, SensorStatus, DataQuality } from '../types';

export const Sensors: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch sensors with fallback to mock data
  const { data: sensors, isLoading, error, refetch } = useQuery({
    queryKey: ['sensors'],
    queryFn: () => sensorApi.getAll(),
    retry: false,
  });

  const sensorsData = sensors?.data || mockData.sensors;

  // Filter sensors based on search query
  const filteredSensors = sensorsData.filter(sensor =>
    sensor.tag.toLowerCase().includes(searchQuery.toLowerCase()) ||
    sensor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    sensor.sensorType.toLowerCase().includes(searchQuery.toLowerCase()) ||
    sensor.assetName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatusColor = (status: SensorStatus) => {
    switch (status) {
      case SensorStatus.Online:
        return 'success';
      case SensorStatus.Offline:
        return 'error';
      case SensorStatus.Maintenance:
        return 'warning';
      case SensorStatus.Error:
        return 'error';
      case SensorStatus.Calibration:
        return 'info';
      default:
        return 'default';
    }
  };

  const getQualityColor = (quality: DataQuality) => {
    switch (quality) {
      case DataQuality.Good:
        return 'success';
      case DataQuality.Bad:
        return 'error';
      case DataQuality.Uncertain:
        return 'warning';
      case DataQuality.Stale:
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatLastReading = (sensor: Sensor) => {
    if (!sensor.lastValue || !sensor.lastReadingAt) return '-';
    const date = new Date(sensor.lastReadingAt);
    const timeAgo = Math.floor((Date.now() - date.getTime()) / 1000 / 60); // minutes ago
    return `${sensor.lastValue.toFixed(sensor.precision)} ${sensor.unit} (${timeAgo}m ago)`;
  };

  const columns: GridColDef[] = [
    {
      field: 'tag',
      headerName: 'Tag',
      width: 150,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2" fontWeight={600}>
            {params.value}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {params.row.name}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'sensorType',
      headerName: 'Type',
      width: 120,
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
          variant="filled"
        />
      ),
    },
    {
      field: 'assetName',
      headerName: 'Asset',
      width: 180,
    },
    {
      field: 'lastReading',
      headerName: 'Last Reading',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2">
            {formatLastReading(params.row)}
          </Typography>
          <Chip
            label={params.row.lastReadingQuality}
            color={getQualityColor(params.row.lastReadingQuality)}
            size="small"
            variant="outlined"
          />
        </Box>
      ),
    },
    {
      field: 'samplingRateSeconds',
      headerName: 'Sample Rate',
      width: 120,
      renderCell: (params) => `${params.value}s`,
    },
    {
      field: 'enableAnomalyDetection',
      headerName: 'Anomaly Detection',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'Enabled' : 'Disabled'}
          color={params.value ? 'success' : 'default'}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<ViewIcon />}
          label="View"
          onClick={() => console.log('View sensor', params.row)}
        />,
        <GridActionsCellItem
          icon={<EditIcon />}
          label="Edit"
          onClick={() => console.log('Edit sensor', params.row)}
        />,
        <GridActionsCellItem
          icon={<DeleteIcon />}
          label="Delete"
          onClick={() => console.log('Delete sensor', params.row)}
        />,
      ],
    },
  ];

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Sensors
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Refresh">
            <IconButton onClick={() => refetch()}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => console.log('Add sensor')}
          >
            Add Sensor
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {handleApiError(error)} - Showing mock data for demonstration.
        </Alert>
      )}

      {/* Search */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search sensors by tag, name, type, or asset..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Data Grid */}
      <Box sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredSensors}
          columns={columns}
          pageSize={25}
          rowsPerPageOptions={[25, 50, 100]}
          disableSelectionOnClick
          sx={{
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid #f0f0f0',
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f8f9fa',
              borderBottom: '2px solid #e0e0e0',
            },
          }}
        />
      </Box>
    </Box>
  );
};
