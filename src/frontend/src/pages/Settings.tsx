import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
} from '@mui/material';
import {
  Api,
  Storage,
  Security,
  Notifications,
  Tune,
  CloudSync,
  HealthAndSafety,
  Timeline,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { healthApi, systemApi } from '../services/api';

export const Settings: React.FC = () => {
  // Fetch system info and health
  const { data: systemInfo } = useQuery({
    queryKey: ['system-info'],
    queryFn: () => systemApi.getInfo(),
    retry: false,
  });

  const { data: healthData } = useQuery({
    queryKey: ['health'],
    queryFn: () => healthApi.getHealth(),
    refetchInterval: 30000,
    retry: false,
  });

  const getHealthColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'healthy':
        return 'success';
      case 'degraded':
        return 'warning';
      case 'unhealthy':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
        Settings
      </Typography>

      <Grid container spacing={3}>
        {/* System Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                System Information
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <Api />
                  </ListItemIcon>
                  <ListItemText
                    primary="Service"
                    secondary={systemInfo?.data?.Service || 'JimThread API Gateway'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Timeline />
                  </ListItemIcon>
                  <ListItemText
                    primary="Version"
                    secondary={systemInfo?.data?.Version || '1.0.0'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CloudSync />
                  </ListItemIcon>
                  <ListItemText
                    primary="Environment"
                    secondary={systemInfo?.data?.Environment || 'Development'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <HealthAndSafety />
                  </ListItemIcon>
                  <ListItemText
                    primary="Status"
                    secondary={
                      <Chip
                        label={systemInfo?.data?.Status || healthData?.data?.status || 'Unknown'}
                        color={getHealthColor(systemInfo?.data?.Status || healthData?.data?.status)}
                        size="small"
                      />
                    }
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Health Checks */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Health Checks
              </Typography>
              {healthData?.data?.checks ? (
                <List dense>
                  {healthData.data.checks.map((check, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Storage />
                      </ListItemIcon>
                      <ListItemText
                        primary={check.name}
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              label={check.status}
                              color={getHealthColor(check.status)}
                              size="small"
                            />
                            <Typography variant="caption" color="text.secondary">
                              {check.duration}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary">
                  Health check data not available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Configuration Categories */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Configuration
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                System configuration will be available in future releases. Currently, configuration 
                is managed through environment variables and configuration files.
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Api color="primary" />
                      <Typography variant="subtitle1" fontWeight={600}>
                        API Settings
                      </Typography>
                      <Chip label="Future" size="small" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Configure API endpoints, timeouts, and rate limiting
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Storage color="success" />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Database
                      </Typography>
                      <Chip label="Future" size="small" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Configure Neo4j, Redis, and other database connections
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Security color="warning" />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Security
                      </Typography>
                      <Chip label="Future" size="small" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Manage authentication, authorization, and security policies
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Notifications color="info" />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Notifications
                      </Typography>
                      <Chip label="Future" size="small" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Configure email, SMS, and webhook notifications
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Tune color="secondary" />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Analytics
                      </Typography>
                      <Chip label="Future" size="small" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Configure ML models, thresholds, and analysis parameters
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Box sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <CloudSync color="primary" />
                      <Typography variant="subtitle1" fontWeight={600}>
                        Integration
                      </Typography>
                      <Chip label="Future" size="small" />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Configure external system integrations and data connectors
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Configuration */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Current Configuration
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                The system is currently configured with the following default settings:
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Database Connections:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Neo4j"
                        secondary="bolt://localhost:7687"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Redis"
                        secondary="localhost:6379"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Kafka"
                        secondary="localhost:9092"
                      />
                    </ListItem>
                  </List>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    API Configuration:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Base URL"
                        secondary="/api/v1"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Timeout"
                        secondary="30 seconds"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Rate Limiting"
                        secondary="100 requests/minute"
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};
