import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Al<PERSON>,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  Precision,
  Sensors,
  Warning,
  CheckCircle,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { assetApi, sensorApi, healthApi, mockData } from '../services/api';
import { AssetStatus, SensorStatus } from '../types';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color, subtitle }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="overline">
            {title}
          </Typography>
          <Typography variant="h4" component="div" sx={{ fontWeight: 600, color }}>
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="textSecondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        <Box sx={{ color, opacity: 0.8 }}>
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

export const Dashboard: React.FC = () => {
  // Fetch data with fallback to mock data
  const { data: assets, isLoading: assetsLoading, error: assetsError } = useQuery({
    queryKey: ['assets'],
    queryFn: () => assetApi.getAll(),
    retry: false,
  });

  const { data: sensors, isLoading: sensorsLoading, error: sensorsError } = useQuery({
    queryKey: ['sensors'],
    queryFn: () => sensorApi.getAll(),
    retry: false,
  });

  const { data: assetStats, isLoading: assetStatsLoading, error: assetStatsError } = useQuery({
    queryKey: ['asset-statistics'],
    queryFn: () => assetApi.getStatistics(),
    retry: false,
  });

  const { data: sensorStats, isLoading: sensorStatsLoading, error: sensorStatsError } = useQuery({
    queryKey: ['sensor-statistics'],
    queryFn: () => sensorApi.getStatistics(),
    retry: false,
  });

  const { data: health, isLoading: healthLoading, error: healthError } = useQuery({
    queryKey: ['health'],
    queryFn: () => healthApi.getHealth(),
    refetchInterval: 30000,
    retry: false,
  });

  // Use mock data if API calls fail
  const assetsData = assets?.data || mockData.assets;
  const sensorsData = sensors?.data || mockData.sensors;
  const assetStatsData = assetStats?.data || mockData.statistics.assets;
  const sensorStatsData = sensorStats?.data || mockData.statistics.sensors;

  // Calculate metrics
  const totalAssets = assetsData.length;
  const onlineAssets = assetsData.filter(a => a.status === AssetStatus.Online).length;
  const totalSensors = sensorsData.length;
  const onlineSensors = sensorsData.filter(s => s.status === SensorStatus.Online).length;
  const criticalAssets = assetsData.filter(a => a.criticalityLevel >= 4).length;

  // Prepare chart data
  const assetStatusData = Object.entries(assetStatsData.assetsByStatus).map(([status, count]) => ({
    name: status,
    value: count,
    color: getStatusColor(status as AssetStatus),
  }));

  const assetTypeData = Object.entries(assetStatsData.assetsByType).map(([type, count]) => ({
    name: type,
    value: count,
  }));

  const sensorTypeData = Object.entries(sensorStatsData.sensorsByType).map(([type, count]) => ({
    name: type,
    value: count,
  }));

  function getStatusColor(status: AssetStatus): string {
    switch (status) {
      case AssetStatus.Online:
        return '#4caf50';
      case AssetStatus.Offline:
        return '#f44336';
      case AssetStatus.Maintenance:
        return '#ff9800';
      case AssetStatus.Error:
        return '#e91e63';
      case AssetStatus.Warning:
        return '#ff5722';
      default:
        return '#9e9e9e';
    }
  }

  const isLoading = assetsLoading || sensorsLoading || assetStatsLoading || sensorStatsLoading;
  const hasErrors = assetsError || sensorsError || assetStatsError || sensorStatsError;

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
        Dashboard
      </Typography>

      {hasErrors && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Some data could not be loaded from the API. Showing mock data for demonstration.
        </Alert>
      )}

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Total Assets"
            value={totalAssets}
            icon={<Precision sx={{ fontSize: 40 }} />}
            color="#1976d2"
            subtitle={`${criticalAssets} critical`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Online Assets"
            value={onlineAssets}
            icon={<CheckCircle sx={{ fontSize: 40 }} />}
            color="#4caf50"
            subtitle={`${Math.round((onlineAssets / totalAssets) * 100)}% uptime`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Total Sensors"
            value={totalSensors}
            icon={<Sensors sx={{ fontSize: 40 }} />}
            color="#ff9800"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Online Sensors"
            value={onlineSensors}
            icon={<TrendingUp sx={{ fontSize: 40 }} />}
            color="#00c49f"
            subtitle={`${Math.round((onlineSensors / totalSensors) * 100)}% active`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <MetricCard
            title="Active Alerts"
            value={0}
            icon={<Warning sx={{ fontSize: 40 }} />}
            color="#f44336"
            subtitle="All clear"
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Asset Status Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Asset Status Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={assetStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {assetStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Asset Types */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Assets by Type
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={assetTypeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#1976d2" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Sensor Types */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Sensors by Type
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={sensorTypeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#ff9800" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Health */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                System Health
              </Typography>
              <Box sx={{ p: 2 }}>
                {healthLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : healthError ? (
                  <Alert severity="error">
                    Unable to connect to backend services
                  </Alert>
                ) : (
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Typography variant="body1">Overall Status:</Typography>
                      <Chip
                        label={health?.data.status || 'Unknown'}
                        color={health?.data.status === 'Healthy' ? 'success' : 'error'}
                        variant="filled"
                      />
                    </Box>
                    {health?.data.checks && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Service Status:
                        </Typography>
                        {health.data.checks.map((check, index) => (
                          <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                            <Typography variant="body2">{check.name}</Typography>
                            <Chip
                              label={check.status}
                              size="small"
                              color={check.status === 'Healthy' ? 'success' : 'error'}
                            />
                          </Box>
                        ))}
                      </Box>
                    )}
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};
